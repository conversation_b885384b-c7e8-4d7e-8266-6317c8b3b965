name: Preview Build

on:
  pull_request:
    types: [opened, synchronize, reopened]

jobs:
  build-preview:
    runs-on: ubuntu-latest
    if: github.repository == 'doocs/md'
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Set up node
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Build
        run: |
          npm install
          npm run build:h5-netlify

      - name: Upload dist artifact
        uses: actions/upload-artifact@v4
        with:
          name: dist
          path: dist
          retention-days: 5

      - name: Save PR number
        if: ${{ always() }}
        run: echo ${{ github.event.number }} > ./pr-id.txt

      - name: Upload PR number
        if: ${{ always() }}
        uses: actions/upload-artifact@v4
        with:
          name: pr
          path: ./pr-id.txt
