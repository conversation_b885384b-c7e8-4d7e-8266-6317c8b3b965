name: Preview Deploy

on:
  workflow_run:
    workflows: ["Preview Build"]
    types:
      - completed

jobs:
  success:
    runs-on: ubuntu-latest
    if: github.event.workflow_run.event == 'pull_request' && github.event.workflow_run.conclusion == 'success' && github.repository == 'doocs/md'
    steps:
      - name: Download PR artifact
        uses: dawidd6/action-download-artifact@v8
        with:
          workflow: ${{ github.event.workflow_run.workflow_id }}
          run_id: ${{ github.event.workflow_run.id }}
          name: pr

      - name: Save PR id
        id: pr
        run: |
          pr_id=$(<pr-id.txt)
          if ! [[ "$pr_id" =~ ^[0-9]+$ ]]; then
            echo "Error: pr-id.txt does not contain a valid numeric PR id. Please check."
            exit 1
          fi
          echo "id=$pr_id" >> $GITHUB_OUTPUT

      - name: Download dist artifact
        uses: dawidd6/action-download-artifact@v8
        with:
          workflow: ${{ github.event.workflow_run.workflow_id }}
          run_id: ${{ github.event.workflow_run.id }}
          workflow_conclusion: success
          name: dist

      - name: Upload surge service
        id: deploy
        run: |
          export DEPLOY_DOMAIN=https://doocs-md-preview-pr-${{ steps.pr.outputs.id }}.surge.sh
          npx surge --project ./ --domain $DEPLOY_DOMAIN --token ${{ secrets.SURGE_TOKEN }}

      - name: Upload status comment
        uses: actions-cool/maintain-one-comment@v2
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          body: |
            🎊 PR Preview has been successfully built and deployed to https://doocs-md-preview-pr-${{ steps.pr.outputs.id }}.surge.sh

            <img width="300" src="https://user-images.githubusercontent.com/507615/90250366-88233900-de6e-11ea-95a5-84f0762ffd39.png">

            <!-- Sticky Pull Request Comment -->
          body-include: '<!-- Sticky Pull Request Comment -->'
          number: ${{ steps.pr.outputs.id }}

      - name: The job failed
        if: ${{ failure() }}
        uses: actions-cool/maintain-one-comment@v2
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          body: |
            😭 Deploy PR Preview failed.

            <img width="300" src="https://user-images.githubusercontent.com/507615/90250824-4e066700-de6f-11ea-8230-600ecc3d6a6b.png">

            <!-- Sticky Pull Request Comment -->
          body-include: '<!-- Sticky Pull Request Comment -->'
          number: ${{ steps.pr.outputs.id }}

  failed:
    runs-on: ubuntu-latest
    if: github.event.workflow_run.event == 'pull_request' && github.event.workflow_run.conclusion == 'failure' && github.repository == 'doocs/md'
    steps:
      - name: Download PR artifact
        uses: dawidd6/action-download-artifact@v2
        with:
          workflow: ${{ github.event.workflow_run.workflow_id }}
          name: pr

      - name: Save PR id
        id: pr
        run: echo "id=$(<pr-id.txt)" >> "$GITHUB_OUTPUT"

      - name: The job failed
        uses: actions-cool/maintain-one-comment@v2
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          body: |
            😭 Deploy PR Preview failed.

            <img width="300" src="https://user-images.githubusercontent.com/507615/90250824-4e066700-de6f-11ea-8230-600ecc3d6a6b.png">

            <!-- Sticky Pull Request Comment -->
          body-include: '<!-- Sticky Pull Request Comment -->'
          number: ${{ steps.pr.outputs.id }}
