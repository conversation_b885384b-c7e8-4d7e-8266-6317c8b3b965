name: Create Cli Release

on:
  push:
    tags:
      - 'cli-v*'

jobs:
  build:
    runs-on: ubuntu-latest
    if: github.repository == 'doocs/md'
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: 20
          registry-url: https://registry.npmjs.org/

      - run: npm install
      - run: npm run build:cli
      - run: echo "registry=https://registry.npmjs.org/" > md-cli/.npmrc

      - run: cd md-cli && npm ci && npm publish --registry=https://registry.npmjs.org/
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}
