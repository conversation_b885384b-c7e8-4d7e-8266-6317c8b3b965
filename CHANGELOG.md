## [v2.0.5] - 2025-06-18

### ✨ 新特性

- **支持 commonmark 的



## [v2.0.3] - 2025-05-25

### ✨ 新特性

- **AI 引用全文 & 快捷指令**：AI 现在可直接引用整篇文档并支持自定义快捷指令，编辑器与 AI 能力深度融合，提升交互效率。  
- **一键清空文档**：新增“清空”按钮，支持一键删除全部内容，快速重置编辑环境。  
- **公众号名片插入**：支持在 Markdown 中便捷插入公众号名片，丰富文章展示形式。  
- **Telegram & Cloudinary 图床**：新增 Telegram、Cloudinary 图床选项，进一步扩充多图床生态。  

### 🛠 功能优化与问题修复

- **AI Prompt 优化**：改进提示词生成策略，使 AI 回答更精准、上下文衔接更自然。  
- **文件名修复**：解决保存与导出时偶发的文件名错误问题。  
- **行内公式样式优化**：调整行内公式渲染样式，提升可读性与排版一致性。  
- **编辑器快捷键优化**：重新梳理常用快捷键映射，操作更顺手。  

### 👏 贡献者

@yanglbme @syhxzzz @YangFong @Nefelibata-Zhu @biggerboy @SiZV200 @XAihan @zzydannyer @quiet-river  

> 感谢所有贡献者的努力！🚀  
> **doocs/md** 将持续为打造更流畅、更强大的 Markdown 创作体验不断前行！

## [v2.0.2] - 2025-05-06

### ✨ 新特性

- **AI 工具箱**：支持智能优化文本、翻译、文本纠错、内容总结等功能，进一步提升内容创作效率。  
- **配置导入与导出**：支持导出、导入配置，实现跨设备同步，简化配置管理。
- **又拍云图床支持**：新增又拍云图床，丰富图床选择。  
- **多预览模式**：支持移动端和电脑端两种预览模式，适配不同设备的阅读和编辑体验。  
- **AI 推理过程展示**：AI 对话功能支持展示推理过程，提升对话透明度与可解释性。
- **脚注支持**：支持 Markdown 脚注功能，方便用户为文档添加注释与引用。

### 🛠 功能优化与问题修复

- **修复消息错乱**：修复 AI 对话过程中删除消息导致消息错乱的问题。  
- **排序问题修复**：修复内容管理模块中排序方式失效的问题。  

### 👏 贡献者

感谢以下贡献者的杰出贡献：

@yanglbme @YangFong @honwhy @wNing50 @zzydannyer @XAihan @dodolalorc @codedogQBY @quiet-river @acbin

> 感谢所有贡献者的努力！🚀  
> **doocs/md** 将持续为打造更流畅、更强大的 Markdown 创作体验不断前行！


## [v2.0.1] - 2025-04-28

### ✨ 新特性

- **AI 能力增强**：支持多种主流 AI 模型，包括 DeepSeek、OpenAI、通讯千问、腾讯混元、智谱 AI、百川智能、月之暗面等。内置默认 AI 服务，用户无需配置 sk，即可免费使用智能助手功能，提升内容创作与处理体验。
- **公众号图片上传体验优化**：通过引入 Cloudflare Functions & Pages，进一步优化了公众号图床的配置与上传体验。
- **内容管理功能提升**：支持自定义内容排序方式，帮助用户更灵活地管理和查找内容。
- **支持导出为 PNG**：可将文档内容一键导出为 PNG 图片，方便快速分享与保存。
- **初步适配移动端**：针对移动端进行了初步适配，优化了浏览与编辑体验，为不同设备使用场景打下基础。

### 🛠 功能优化与问题修复

- **主题样式修复**：修复了部分主题存在的样式问题，提升整体界面一致性和视觉体验。
- **编辑界面优化**：优化了编辑器的界面布局与交互体验，提升用户操作的流畅性。

### 👏 贡献者

感谢以下贡献者的杰出贡献：

@honwhy @YangFong @ting772 @yanglbme @acbin @chinenkai @wNing50

> 感谢所有贡献者的努力！🚀  
> **doocs/md** 将持续为打造更流畅、更强大的 Markdown 创作体验不断前行！

## [v2.0.0] - 2025-04-18

### 1. 新特性亮点

- **数学公式与 Mermaid 流程图支持**：全面支持 Markdown 基础语法、数学公式、Mermaid 图表等，提升内容表达能力。
- **自定义样式面板**：新增样式自定义面板，支持主题色和 CSS 定制，适配浅/暗模式。
- **本地内容管理**：支持一键导入导出和自动草稿保存，提升编辑效率与安全性。
- **图床支持扩展**：新增公众号与 Cloudflare R2 图床支持，灵活的上传逻辑配置。
- **插件支持**：新增浏览器扩展插件，支持 Chrome、Edge、Firefox 等主流浏览器。
- **AI 助手集成**：集成智能 AI 助手功能，支持与主流 AI 模型（如 DeepSeek、OpenAI、通义千问）进行自然语言对话，辅助内容创作、语法优化、格式转换等场景，极大提升写作效率。

### 2. 框架、镜像升级

- **Node.js 20+ 与 Vue3 + Vite**：全面升级依赖，基于 Vue3 和 Vite，显著提升性能与兼容性。
- **Docker 多架构镜像**：支持 `linux/arm64` 和 `linux/amd64` 多架构镜像。

### 3. 贡献者

@YangFong @yanglbme @honwhy @bravekingzhang @dribble-njr @lurenyang418 @chensirup @wll8 @thinkasany @arunsathiya @realskyrin @rwecho

## [v1.6.0] - 2023-12-05

### 1. 新特性亮点

- **Mac 风格代码块样式支持**：增加 Mac 风格的代码块渲染样式，提升视觉一致性与可读性。
- **LATEX 数学公式支持**：引入 LATEX 编辑与渲染能力，支持科学公式表达，适用于技术写作与学术场景。

### 2. 功能优化与修复

- **组件重构与性能优化**：对部分组件结构进行重构与优化，提升整体性能与维护性。
- **Bug 修复**：修复部分用户反馈的问题，提升使用稳定性与用户体验。

### 3. 框架与部署支持

- **Node 版本升级**：升级 Node.js 版本以增强兼容性和构建性能。
- **Docker 镜像同步推送**：更新版本已同步发布至 Docker Hub，可通过以下命令快速启动本地实例 `docker run -d -p 8080:80 doocs/md:latest`

### 4. 贡献者

@YangFong @yanglbme @bravekingzhang @DandelionCloud
