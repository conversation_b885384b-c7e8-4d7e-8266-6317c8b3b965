# 浏览器扩展安装指南

本项目已成功构建为支持 Chrome、Firefox 和 Edge 的浏览器扩展。

## 📦 扩展包文件

构建完成后，您可以在 `.output` 目录中找到以下文件：

- `md-2.0.3-chrome.zip` - Chrome/Edge 扩展包 (Manifest V3)
- `md-2.0.3-firefox.zip` - Firefox 扩展包 (Manifest V2)
- `md-2.0.3-sources.zip` - 源代码包

## 🚀 安装方法

### Chrome 浏览器安装

1. **打开扩展管理页面**

   - 在地址栏输入 `chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

2. **启用开发者模式**

   - 点击右上角的"开发者模式"开关

3. **安装扩展**
   - 点击"加载已解压的扩展程序"
   - 选择解压后的 `chrome-mv3` 文件夹
   - 或者直接拖拽 `md-2.0.3-chrome.zip` 到页面上

### Firefox 浏览器安装

1. **打开附加组件管理页面**

   - 在地址栏输入 `about:addons`
   - 或者：菜单 → 附加组件和主题

2. **临时安装**
   - 点击设置图标（齿轮）
   - 选择"调试附加组件"
   - 点击"临时载入附加组件"
   - 选择 `md-2.0.3-firefox.zip` 文件

### Edge 浏览器安装

1. **打开扩展管理页面**

   - 在地址栏输入 `edge://extensions/`
   - 或者：菜单 → 扩展

2. **启用开发者模式**

   - 点击左下角的"开发人员模式"开关

3. **安装扩展**
   - 点击"加载解压缩的扩展"
   - 选择解压后的 `chrome-mv3` 文件夹

## 🎯 扩展功能

安装完成后，扩展提供以下功能：

### 主要入口

1. **Popup 弹窗** - 点击扩展图标打开小窗口
2. **Options 选项页** - 右键扩展图标 → 选项
3. **Side Panel 侧边栏** - 在支持的浏览器中显示侧边栏

### 核心功能

- ✅ **完整的 Markdown 编辑器**
- ✅ **实时预览**
- ✅ **CommonMark Admonition 支持**
- ✅ **代码高亮**
- ✅ **数学公式渲染 (KaTeX)**
- ✅ **Mermaid 图表**
- ✅ **文件导入/导出**
- ✅ **多种主题**

## 🔧 开发和构建

### 开发模式

```bash
# 启动开发服务器
npm run dev

# 启动扩展开发模式
npm run ext:dev

# Firefox 开发模式
npm run firefox:dev
```

### 生产构建

```bash
# 构建 Chrome 扩展
npm run ext:zip

# 构建 Firefox 扩展
npm run firefox:zip
```

## 📋 技术规格

- **Chrome/Edge**: Manifest V3
- **Firefox**: Manifest V2
- **框架**: Vue 3 + TypeScript
- **构建工具**: WXT (Web Extension Toolkit)
- **包大小**:
  - Chrome: ~4.08 MB
  - Firefox: ~4.08 MB

## 🛠️ 故障排除

### 常见问题

1. **扩展无法加载**

   - 确保已启用开发者模式
   - 检查文件路径是否正确
   - 查看浏览器控制台错误信息

2. **功能异常**

   - 刷新扩展页面
   - 重新加载扩展
   - 检查浏览器版本兼容性

3. **Firefox 临时安装限制**
   - Firefox 的临时安装在浏览器重启后会失效
   - 需要重新安装或考虑发布到 Firefox Add-ons

### 日志查看

- **Chrome**: 开发者工具 → Console
- **Firefox**: 浏览器控制台 (Ctrl+Shift+J)
- **Edge**: 开发者工具 → Console

## 📝 注意事项

1. **权限要求**: 扩展需要访问活动标签页权限
2. **存储**: 使用浏览器本地存储保存用户数据
3. **更新**: 手动安装的扩展需要手动更新
4. **兼容性**: 支持现代浏览器的最新版本

## 🎉 使用建议

1. **固定扩展**: 右键扩展图标选择"固定"以便快速访问
2. **快捷键**: 可在浏览器扩展设置中配置快捷键
3. **数据备份**: 定期导出重要的 Markdown 文件
4. **主题选择**: 根据使用环境选择合适的编辑器主题

---

**享受您的 Markdown 编辑体验！** 🚀
