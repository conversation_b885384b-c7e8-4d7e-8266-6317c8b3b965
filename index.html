<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="keywords" content="md,markdown,markdown-editor,marktwain,zillionare,github-pages" />
    <meta name="description" content="MarkTwain | 一次编辑，到处发布 - 增强版 Markdown 编辑器" />
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    />
    <title>MarkTwain | 一次编辑，到处发布</title>
    <link rel="shortcut icon" href="https://cdn-doocs.oss-cn-shenzhen.aliyuncs.com/gh/doocs/md/images/favicon.png" />
    <link
      rel="apple-touch-icon-precomposed"
      href="https://cdn-doocs.oss-cn-shenzhen.aliyuncs.com/gh/doocs/md/images/1648303220922-7e14aefa-816e-44c1-8604-ade709ca1c69.png"
    />
    <style>
      .loading {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 99999;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100vw;
        height: 100vh;
        font-size: 18px;
      }

      .loading::before {
        content: url('/src/assets/images/favicon.png');
        width: 100px;
        height: 100px;
        margin-bottom: 26px;
      }

      .loading.dark {
        color: #ffffff;
        background-color: #141414;
      }

      .loading .txt {
        position: absolute;
        bottom: 10%;
      }

      .loading .txt::after {
        content: '...';
        animation: dots 1.5s steps(4, end) infinite;
      }

      @keyframes dots {
        0% {
          content: ' ';
        }
        25% {
          content: '.';
        }
        50% {
          content: '..';
        }
        75% {
          content: '...';
        }
      }
    </style>
  </head>

  <body>
    <noscript>
      <strong>Please enable JavaScript to continue.</strong>
    </noscript>
    <div id="app">
      <div class="loading">
        <strong>MarkTwain | 一次编辑，到处发布</strong>
        <p class="txt">正在加载编辑器</p>
      </div>
    </div>
    <script>
      const theme = localStorage.getItem('vueuse-color-scheme')
      if (theme === 'dark' || (theme === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        document.querySelector('.loading').classList.add('dark')
      }
    </script>
    <script>
      window.MathJax = {
        loader: { load: ['[tex]/ams'] },
        tex: { packages: { '[+]': ['ams'] }, tags: 'ams' },
        svg: { fontCache: 'none' },
      }
    </script>
    <script
      id="MathJax-script"
      src="https://cdn-doocs.oss-cn-shenzhen.aliyuncs.com/npm/mathjax@3/es5/tex-svg.js"
    ></script>
    <script type="module" src="/src/main.ts"></script>
    <script type="module" src="/src/sidepanel.ts"></script>
  </body>
  <script src="https://cdn-doocs.oss-cn-shenzhen.aliyuncs.com/gh/wechatsync/article-syncjs@latest/dist/main.js"></script>
</html>
