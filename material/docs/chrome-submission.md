# Chrome Web Store 提交指南

## 📋 提交前准备

### 1. 开发者账户注册

1. 访问 [Chrome Web Store 开发者控制台](https://chrome.google.com/webstore/devconsole/)
2. 使用 Google 账户登录
3. 支付一次性注册费用 $5 USD
4. 同意开发者协议
5. 验证开发者身份（可能需要提供身份证明）

### 2. 扩展包准备

```bash
# 构建 Chrome 扩展包
npm install
npm run ext:zip

# 生成的文件位置
.output/marktwain-2.0.5-chrome.zip
```

### 3. 必需素材准备

- **扩展包**：`marktwain-2.0.5-chrome.zip`
- **图标**：16×16, 48×48, 128×128 PNG 格式
- **截图**：1-5张，推荐 1280×800 像素
- **推广图片**（可选）：440×280, 1400×560 像素

## 🚀 详细提交步骤

### 步骤 1：上传扩展包

1. 登录 Chrome Web Store 开发者控制台
2. 点击 **"添加新项目"**
3. 选择并上传 `marktwain-2.0.5-chrome.zip`
4. 等待自动验证完成（通常几分钟）
5. 如有错误，根据提示修复后重新上传

### 步骤 2：填写基本信息

#### 扩展名称

```
MarkTwain
```

#### 简短描述（132字符以内）

```
一处编辑，处处发布！支持微信公众号、知乎、CSDN等多平台的专业Markdown编辑器，让内容创作更高效。
```

#### 详细描述

使用 `material/chrome/description.md` 中的内容，包含：

- 功能特性介绍
- 使用场景说明
- 安装使用指南
- 隐私保护说明
- 联系方式

### 步骤 3：上传视觉素材

#### 图标上传

- **小图标**：从 `material/chrome/icons/icon-16.png` 上传
- **中图标**：从 `material/chrome/icons/icon-48.png` 上传
- **大图标**：从 `material/chrome/icons/icon-128.png` 上传

#### 截图上传

按顺序上传以下截图：

1. **主界面截图**：展示编辑器的整体界面
2. **编辑功能截图**：展示 Markdown 编辑和预览
3. **发布功能截图**：展示多平台发布界面
4. **主题展示截图**：展示不同主题效果
5. **设置界面截图**：展示配置选项

#### 推广图片（可选但推荐）

- **小型推广图片**：440×280 像素
- **大型推广图片**：1400×560 像素
- **截图推广图片**：640×400 像素

### 步骤 4：设置分类和标签

#### 主要分类

选择：**生产力工具 (Productivity)**

#### 次要分类

选择：**开发者工具 (Developer Tools)**

#### 关键词标签

```
markdown, editor, writing, publishing, wechat, zhihu, csdn, blog, productivity
```

### 步骤 5：隐私设置

#### 隐私政策

- 提供隐私政策链接：`https://zillionare.github.io/marktwain/privacy`
- 或直接粘贴 `material/chrome/privacy-policy.md` 的内容

#### 权限说明

在描述中详细说明每个权限的用途：

- **storage**：保存用户文档和设置
- **tabs**：获取当前标签页信息用于发布
- **activeTab**：访问当前活动标签页
- **sidePanel**：显示侧边栏编辑器
- **contextMenus**：添加右键菜单选项

#### 主机权限说明

- **github.com**：发布到 GitHub Pages
- **githubusercontent.com**：访问 GitHub 图床
- **gitee.com**：发布到 Gitee Pages
- **weixin.qq.com**：发布到微信公众号
- **qpic.cn**：访问微信图片资源

### 步骤 6：发布设置

#### 可见性设置

- 选择：**公开** (Public)
- 地区：**所有地区**
- 语言：**中文（简体）** 和 **英语**

#### 定价

- 选择：**免费**

#### 分发设置

- 启用：**Chrome Web Store**
- 启用：**企业用户**（如适用）

### 步骤 7：提交审核

#### 最终检查

- [ ] 所有必填字段已完成
- [ ] 图标和截图已上传
- [ ] 描述内容准确完整
- [ ] 隐私政策已提供
- [ ] 权限说明清晰

#### 提交审核

1. 点击 **"提交审核"**
2. 确认提交信息
3. 等待审核结果

## ⏰ 审核时间和流程

### 审核时间

- **自动审核**：几分钟到几小时
- **人工审核**：1-3个工作日
- **复杂情况**：最多7个工作日

### 审核状态

- **待审核**：已提交，等待审核
- **审核中**：正在进行审核
- **需要修改**：审核未通过，需要修改
- **已发布**：审核通过，已上架

### 审核结果处理

- **通过**：扩展自动发布到商店
- **拒绝**：查看拒绝原因，修改后重新提交
- **需要信息**：提供审核团队要求的额外信息

## 🔧 常见问题解决

### 权限相关问题

**问题**：权限过度或说明不清
**解决**：

- 移除不必要的权限
- 在描述中详细说明每个权限的用途
- 提供权限使用的具体示例

### 功能描述问题

**问题**：功能描述与实际不符
**解决**：

- 确保描述准确反映扩展功能
- 提供详细的使用说明
- 更新截图以匹配当前版本

### 隐私政策问题

**问题**：隐私政策缺失或不完整
**解决**：

- 提供完整的隐私政策
- 详细说明数据收集和使用方式
- 确保隐私政策链接可访问

### 图标质量问题

**问题**：图标模糊或不符合规范
**解决**：

- 使用高质量的 PNG 图标
- 确保在不同尺寸下都清晰可见
- 保持品牌一致性

## 📈 发布后优化

### 监控指标

- 安装量和活跃用户数
- 用户评分和评论
- 崩溃报告和错误日志
- 使用统计数据

### 持续改进

- 定期更新扩展功能
- 及时修复用户反馈的问题
- 优化性能和用户体验
- 添加新功能和平台支持

### 用户互动

- 及时回复用户评论
- 收集用户反馈和建议
- 在更新日志中说明改进内容
- 通过官方渠道与用户沟通

## 📞 获取帮助

### 官方资源

- **开发者文档**：https://developer.chrome.com/docs/webstore/
- **政策指南**：https://developer.chrome.com/docs/webstore/program-policies/
- **最佳实践**：https://developer.chrome.com/docs/webstore/best-listing/

### 社区支持

- **开发者论坛**：https://groups.google.com/a/chromium.org/g/chromium-extensions
- **Stack Overflow**：使用 `chrome-extension` 标签

### 联系支持

- **开发者支持**：通过开发者控制台提交支持请求
- **政策问题**：<EMAIL>

---

**按照以上步骤操作，您的 MarkTwain 扩展将顺利提交到 Chrome Web Store！**
