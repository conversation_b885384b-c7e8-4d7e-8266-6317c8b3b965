# Firefox Add-ons 提交指南

## 📋 提交前准备

### 1. 开发者账户注册

1. 访问 [addons.mozilla.org](https://addons.mozilla.org/)
2. 点击右上角 **"登录"**
3. 使用 Firefox 账户登录（或注册新账户）
4. 完善开发者资料信息
5. 验证邮箱地址

### 2. 扩展包准备

```bash
# 构建 Firefox 扩展包
npm install
npm run firefox:zip

# 生成的文件位置
.output/marktwain-2.0.5-firefox.zip
```

### 3. 必需素材准备

- **扩展包**：`marktwain-2.0.5-firefox.zip`
- **图标**：48×48, 96×96, 128×128 PNG 格式
- **截图**：最多10张，推荐 1280×800 像素
- **隐私政策**：如收集用户数据则必需

## 🚀 详细提交步骤

### 步骤 1：开始提交流程

1. 登录 [addons.mozilla.org](https://addons.mozilla.org/)
2. 点击 **"提交新附加组件"**
3. 选择 **"在此网站上"** (List on this site)
4. 点击 **"继续"**

### 步骤 2：上传扩展包

1. 点击 **"选择文件"**
2. 选择 `marktwain-2.0.5-firefox.zip`
3. 等待文件上传和自动验证
4. 如有验证错误，根据提示修复后重新上传
5. 选择兼容的平台（通常选择所有平台）
6. 点击 **"继续"**

### 步骤 3：源代码提交（如需要）

如果扩展包含混淆代码或使用了构建工具：

1. 选择 **"是，我的扩展需要源代码提交"**
2. 上传包含源代码的 ZIP 文件
3. 在说明中描述构建过程
4. 点击 **"继续"**

### 步骤 4：填写附加组件信息

#### 基本信息

**名称**：

```
MarkTwain
```

**附加组件 URL**：

```
marktwain
```

（系统会自动生成，如被占用需要修改）

**摘要**（250字符以内）：

```
MarkTwain - 专业的Markdown编辑器浏览器扩展。支持微信公众号、知乎、CSDN等多平台一键发布，内置丰富主题和AI助手，让内容创作更高效。一处编辑，处处发布！
```

#### 详细描述

使用 `material/firefox/description.md` 中的内容，支持 Markdown 格式：

```markdown
# 🚀 MarkTwain - 让内容创作更高效

MarkTwain 是一款专为内容创作者设计的浏览器扩展...
（完整内容见 description.md 文件）
```

#### 分类设置

- **Firefox 分类**：选择 **"其他"** 或 **"生产力"**
- **Firefox for Android 分类**：选择 **"其他"**

#### 标签

```
markdown, editor, writing, publishing, wechat, zhihu, csdn, juejin, blog, technical-writing, productivity, content-creation, ai-assistant, multi-platform
```

#### 支持信息

- **支持邮箱**：`<EMAIL>`
- **支持网站**：`https://github.com/zillionare/marktwain`

#### 许可证

选择：**MIT License**

#### 隐私政策

- 勾选 **"此附加组件有隐私政策"**
- 粘贴 `material/firefox/privacy-policy.md` 的内容

#### 审核者说明

```
MarkTwain 是一个开源的 Markdown 编辑器浏览器扩展。

主要功能：
1. Markdown 编辑和实时预览
2. 多平台内容发布（微信公众号、知乎、CSDN等）
3. 图床集成和图片管理
4. 主题定制和样式管理
5. AI 助手功能

权限说明：
- storage: 保存用户文档和设置
- tabs: 获取标签页信息用于发布功能
- activeTab: 访问当前活动标签页
- sidePanel: 显示侧边栏编辑器
- contextMenus: 添加右键菜单选项

主机权限用于访问各个发布平台的 API 和图床服务。

源代码：https://github.com/zillionare/marktwain
```

### 步骤 5：上传视觉素材

#### 图标上传

- **48×48 图标**：从 `material/firefox/icons/icon-48.png` 上传
- **96×96 图标**：从 `material/firefox/icons/icon-96.png` 上传
- **128×128 图标**：从 `material/firefox/icons/icon-128.png` 上传

#### 截图上传

按重要性顺序上传截图：

1. **主界面截图**：展示编辑器整体界面
2. **编辑功能截图**：展示 Markdown 编辑和预览
3. **发布功能截图**：展示多平台发布界面
4. **主题展示截图**：展示不同主题效果
5. **设置界面截图**：展示配置选项

每张截图可以添加说明文字。

### 步骤 6：最终设置

#### 实验性标记

- 如果扩展功能稳定，不勾选 **"此附加组件是实验性的"**
- 如果是测试版本，可以勾选此选项

#### 付费功能

- 不勾选 **"此附加组件需要付费、非免费服务或软件，或额外硬件"**

#### 平台兼容性

- 选择支持的 Firefox 版本（建议选择最新的稳定版本）
- 选择支持的操作系统（通常选择所有）

### 步骤 7：提交审核

1. 检查所有信息是否完整准确
2. 点击 **"提交版本"**
3. 确认提交信息
4. 等待审核结果

## ⏰ 审核流程和时间

### 审核类型

- **自动审核**：基本检查，几分钟内完成
- **人工审核**：详细审查，可能需要几小时到几天
- **完整审核**：包含源代码审查，可能需要更长时间

### 审核状态

- **等待审核**：已提交，排队等待
- **审核中**：正在进行审核
- **已批准**：审核通过，已发布
- **需要修改**：需要修改后重新提交
- **已拒绝**：审核未通过

### 审核标准

- **功能性**：扩展功能正常工作
- **安全性**：没有安全漏洞或恶意代码
- **隐私性**：符合隐私政策要求
- **质量**：代码质量和用户体验良好
- **政策合规**：符合 Mozilla 附加组件政策

## 🔧 常见问题解决

### 权限相关问题

**问题**：权限请求不合理或说明不清
**解决方案**：

- 在描述中详细说明每个权限的用途
- 移除不必要的权限
- 提供权限使用的具体示例

### 隐私政策问题

**问题**：隐私政策不完整或缺失
**解决方案**：

- 提供详细的隐私政策
- 说明数据收集、使用和存储方式
- 包含用户权利和联系方式

### 源代码问题

**问题**：源代码审查未通过
**解决方案**：

- 提供完整的源代码
- 包含详细的构建说明
- 确保源代码与提交的扩展包一致

### 功能描述问题

**问题**：功能描述不准确或不完整
**解决方案**：

- 确保描述准确反映扩展功能
- 提供详细的使用说明和示例
- 更新截图以匹配当前版本

## 📈 发布后管理

### 版本更新

1. 修改扩展代码
2. 更新版本号
3. 构建新的扩展包
4. 在 AMO 上传新版本
5. 填写更新日志
6. 提交审核

### 用户反馈管理

- 及时回复用户评论和评分
- 收集用户反馈和建议
- 在更新中修复用户报告的问题
- 通过支持渠道提供帮助

### 统计数据监控

- 查看安装量和活跃用户数
- 分析用户评分和评论趋势
- 监控崩溃报告和错误日志
- 跟踪功能使用情况

## 📞 获取帮助

### 官方资源

- **扩展工作坊**：https://extensionworkshop.com/
- **开发者文档**：https://developer.mozilla.org/docs/Mozilla/Add-ons/WebExtensions
- **政策指南**：https://extensionworkshop.com/documentation/publish/add-on-policies/

### 社区支持

- **社区论坛**：https://discourse.mozilla.org/c/add-ons/
- **Matrix 聊天室**：https://chat.mozilla.org/#/room/#addons:mozilla.org
- **Stack Overflow**：使用 `firefox-addon` 标签

### 直接支持

- **开发者支持**：通过 AMO 开发者页面提交支持请求
- **政策问题**：<EMAIL>

---

**按照以上步骤操作，您的 MarkTwain 扩展将顺利提交到 Firefox Add-ons 商店！**
