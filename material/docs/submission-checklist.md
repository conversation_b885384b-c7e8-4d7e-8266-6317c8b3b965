# MarkTwain 浏览器扩展上架清单

## 📋 提交前检查清单

### 🔧 技术准备

#### 扩展包构建

- [ ] 安装项目依赖：`npm install`
- [ ] 构建 Chrome 扩展包：`npm run ext:zip`
- [ ] 构建 Firefox 扩展包：`npm run firefox:zip`
- [ ] 验证扩展包文件完整性
- [ ] 测试扩展在目标浏览器中的功能

#### 代码质量检查

- [ ] 运行代码检查：`npm run lint`
- [ ] 运行类型检查：`npm run type-check`
- [ ] 确保没有安全漏洞
- [ ] 验证所有权限的必要性
- [ ] 检查是否有未使用的权限

### 📁 文件准备

#### Chrome Web Store 必需文件

- [ ] 扩展包文件：`marktwain-chrome.zip`
- [ ] 图标文件：
  - [ ] `icon-16.png` (16×16)
  - [ ] `icon-48.png` (48×48)
  - [ ] `icon-128.png` (128×128)
- [ ] 截图文件（1-5张，1280×800或640×400）：
  - [ ] `screenshot-1.png` - 主界面展示
  - [ ] `screenshot-2.png` - 编辑功能演示
  - [ ] `screenshot-3.png` - 发布功能展示
- [ ] 推广图片（可选）：
  - [ ] `tile-440x280.png`
  - [ ] `marquee-1400x560.png`
- [ ] 商店描述文本
- [ ] 隐私政策链接

#### Firefox Add-ons 必需文件

- [ ] 扩展包文件：`marktwain-firefox.zip`
- [ ] 图标文件：
  - [ ] `icon-48.png` (48×48)
  - [ ] `icon-96.png` (96×96)
  - [ ] `icon-128.png` (128×128)
- [ ] 截图文件（最多10张）：
  - [ ] `screenshot-1.png` - 主界面展示
  - [ ] `screenshot-2.png` - 编辑功能演示
  - [ ] `screenshot-3.png` - 发布功能展示
- [ ] 商店描述文本
- [ ] 隐私政策文档

### 📝 内容准备

#### 商店描述

- [ ] Chrome 简短描述（≤132字符）
- [ ] Chrome 详细描述（≤16,000字符）
- [ ] Firefox 摘要（≤250字符）
- [ ] Firefox 详细描述（无限制，支持Markdown）
- [ ] 功能特性列表
- [ ] 使用说明
- [ ] 更新日志

#### 法律文档

- [ ] 隐私政策（必需，如收集数据）
- [ ] 使用条款（推荐）
- [ ] 开源许可证说明

### 🖼️ 视觉素材

#### 图标要求检查

- [ ] 所有图标为PNG格式
- [ ] 背景透明
- [ ] 图标清晰，在小尺寸下可辨识
- [ ] 符合各平台的设计规范
- [ ] 品牌一致性

#### 截图要求检查

- [ ] 展示核心功能
- [ ] 图片清晰，无模糊
- [ ] 按重要性排序
- [ ] 真实使用场景
- [ ] 符合尺寸要求

### 🔐 隐私和安全

#### 权限审查

- [ ] 审查所有请求的权限
- [ ] 确保权限使用的必要性
- [ ] 在描述中解释权限用途
- [ ] 移除不必要的权限

#### 隐私政策

- [ ] 详细说明数据收集类型
- [ ] 说明数据使用目的
- [ ] 描述数据存储方式
- [ ] 提供用户控制选项
- [ ] 包含联系方式

### 🧪 测试验证

#### 功能测试

- [ ] 核心编辑功能正常
- [ ] 多平台发布功能正常
- [ ] 图片上传功能正常
- [ ] 主题切换功能正常
- [ ] 设置保存功能正常

#### 兼容性测试

- [ ] Chrome 最新版本
- [ ] Chrome 稳定版本
- [ ] Firefox 最新版本
- [ ] Firefox ESR 版本
- [ ] 不同操作系统（Windows, macOS, Linux）

#### 性能测试

- [ ] 启动速度测试
- [ ] 内存使用测试
- [ ] 大文档处理测试
- [ ] 网络请求性能测试

## 🚀 提交流程

### Chrome Web Store 提交步骤

1. **准备开发者账户**

   - [ ] 注册 Chrome Web Store 开发者账户
   - [ ] 支付一次性注册费用（$5）
   - [ ] 验证开发者身份

2. **上传扩展**

   - [ ] 登录 Chrome Web Store 开发者控制台
   - [ ] 点击"添加新项目"
   - [ ] 上传扩展包 ZIP 文件
   - [ ] 等待自动验证完成

3. **填写商店信息**

   - [ ] 添加扩展名称和描述
   - [ ] 上传图标和截图
   - [ ] 选择分类和语言
   - [ ] 设置价格（免费）
   - [ ] 添加隐私政策链接

4. **提交审核**
   - [ ] 检查所有信息
   - [ ] 提交审核
   - [ ] 等待审核结果（通常1-3个工作日）

### Firefox Add-ons 提交步骤

1. **准备开发者账户**

   - [ ] 注册 Mozilla 开发者账户
   - [ ] 验证邮箱地址
   - [ ] 完善开发者资料

2. **上传扩展**

   - [ ] 登录 addons.mozilla.org
   - [ ] 点击"提交新附加组件"
   - [ ] 选择"在此网站上"
   - [ ] 上传扩展包文件

3. **填写附加组件信息**

   - [ ] 添加名称和摘要
   - [ ] 编写详细描述
   - [ ] 上传图标和截图
   - [ ] 选择分类和标签
   - [ ] 添加隐私政策

4. **提交审核**
   - [ ] 检查所有信息
   - [ ] 提交审核
   - [ ] 等待审核结果（通常几小时到几天）

## ⚠️ 常见问题和注意事项

### Chrome Web Store 常见拒绝原因

- [ ] 权限过度：请求了不必要的权限
- [ ] 描述不准确：功能描述与实际不符
- [ ] 图标质量差：图标模糊或不专业
- [ ] 隐私政策缺失：收集数据但未提供隐私政策
- [ ] 功能重复：与现有扩展功能高度重复

### Firefox Add-ons 常见拒绝原因

- [ ] 代码质量问题：存在安全漏洞或性能问题
- [ ] 权限说明不清：未充分解释权限用途
- [ ] 隐私政策不完整：未详细说明数据处理方式
- [ ] 功能描述不准确：实际功能与描述不符
- [ ] 违反内容政策：包含不当内容或功能

### 提交技巧

- [ ] 详细的功能描述有助于审核通过
- [ ] 高质量的截图能提升用户转化率
- [ ] 及时回复审核团队的问题
- [ ] 保持扩展的定期更新
- [ ] 积极回应用户反馈

## 📞 支持和帮助

### 官方资源

- **Chrome Web Store 开发者文档**：https://developer.chrome.com/docs/webstore/
- **Firefox 扩展工作坊**：https://extensionworkshop.com/
- **Chrome Web Store 政策**：https://developer.chrome.com/docs/webstore/program-policies/
- **Firefox 附加组件政策**：https://extensionworkshop.com/documentation/publish/add-on-policies/

### 社区支持

- **Chrome 扩展开发者论坛**：https://groups.google.com/a/chromium.org/g/chromium-extensions
- **Firefox 附加组件社区**：https://discourse.mozilla.org/c/add-ons/
- **Stack Overflow**：使用相关标签搜索问题

### 项目支持

- **GitHub Issues**：https://github.com/zillionare/marktwain/issues
- **邮箱支持**：<EMAIL>

---

**完成以上检查清单后，您的 MarkTwain 扩展就可以成功提交到各大浏览器商店了！**
