# MarkTwain Firefox 扩展隐私政策

**生效日期：2024年1月15日**
**最后更新：2024年1月15日**

## 隐私承诺

MarkTwain Firefox 扩展（以下简称"本扩展"）严格遵循 Mozilla 的隐私原则，致力于保护用户隐私，确保数据安全。我们相信隐私是一项基本权利，因此在设计和开发过程中始终将用户隐私放在首位。

## 数据收集原则

### 最小化原则

我们仅收集提供服务所必需的最少量数据，不会收集与功能无关的信息。

### 透明化原则

我们清楚地告知用户收集哪些数据、如何使用这些数据，以及用户如何控制自己的数据。

### 用户控制原则

用户对自己的数据拥有完全控制权，可以随时查看、修改或删除数据。

## 收集的数据类型

### 1. 用户内容数据

**收集内容：**

- 用户创建的 Markdown 文档
- 自定义主题和样式设置
- 上传的图片和媒体文件
- 发布平台配置信息

**收集目的：**

- 提供文档编辑和存储功能
- 实现多平台发布服务
- 保持用户个性化设置

**存储位置：**

- 主要存储在 Firefox 本地存储中
- 用户可选择启用云端同步（需明确授权）

### 2. 技术数据

**收集内容：**

- 扩展使用统计（功能使用频率、会话时长）
- 错误日志和性能数据
- Firefox 版本和操作系统信息

**收集目的：**

- 改进扩展性能和稳定性
- 修复错误和漏洞
- 优化用户体验

**数据处理：**

- 所有技术数据均匿名化处理
- 不包含任何个人身份信息

### 3. 我们不收集的数据

- 个人身份信息（姓名、邮箱、电话号码）
- 浏览历史和网站访问记录
- 其他标签页或窗口的内容
- 密码或其他敏感认证信息
- 与扩展功能无关的任何数据

## 数据使用方式

### 核心功能提供

- **文档管理**：保存、编辑、组织用户的 Markdown 文档
- **实时预览**：渲染 Markdown 内容为 HTML 格式
- **多平台发布**：将内容适配并发布到各个平台
- **主题定制**：应用和保存用户的样式偏好

### 服务改进

- **性能优化**：分析使用模式以提升扩展性能
- **功能开发**：基于用户需求开发新功能
- **错误修复**：识别和解决技术问题
- **用户体验**：改进界面设计和交互流程

### 技术支持

- **问题诊断**：帮助用户解决使用中遇到的问题
- **故障排除**：快速定位和修复技术故障

## 数据存储和安全

### 本地存储

- **主要存储方式**：使用 Firefox 的 WebExtensions Storage API
- **数据加密**：敏感数据在本地加密存储
- **访问控制**：仅本扩展可访问存储的数据
- **数据清理**：卸载扩展时自动清理所有本地数据

### 云端同步（可选功能）

- **用户选择**：完全由用户决定是否启用
- **数据加密**：传输和存储过程中全程加密
- **服务提供商**：使用符合 GDPR 标准的云服务
- **随时禁用**：用户可随时关闭云端同步功能

### 安全措施

- **传输加密**：所有网络通信使用 HTTPS/TLS 加密
- **数据完整性**：使用校验和确保数据完整性
- **访问日志**：记录数据访问日志以监控异常活动
- **定期审计**：定期进行安全审计和漏洞扫描

## 第三方服务集成

### 内容发布平台

**集成平台：**

- 微信公众号、知乎、CSDN、掘金等

**数据处理：**

- 仅在用户明确授权时访问平台 API
- 不存储用户的登录凭据
- 仅传输用户指定发布的内容

**用户控制：**

- 用户可随时撤销平台授权
- 可选择性发布到特定平台

### 图床服务

**支持服务：**

- GitHub、七牛云、腾讯云 COS、阿里云 OSS

**数据处理：**

- 仅上传用户主动选择的图片
- 使用用户提供的 API 密钥
- 不访问图床服务中的其他数据

### AI 服务

**功能用途：**

- 内容优化建议
- 语法检查和改进
- 自动摘要生成

**隐私保护：**

- 仅在用户主动使用时调用
- 不永久存储用户内容
- 使用匿名化处理

## 用户权利和控制

### 数据访问权

- 查看扩展收集的所有数据
- 了解数据的使用方式和目的
- 获取数据处理的详细信息

### 数据更正权

- 修改不准确的信息
- 更新过时的数据
- 纠正错误的设置

### 数据删除权

- 删除特定的文档或数据
- 清空所有本地存储数据
- 卸载扩展时自动清理

### 数据可携带权

- 导出所有文档和设置
- 以标准格式获取数据
- 迁移到其他工具或服务

### 处理限制权

- 暂停特定数据的处理
- 限制数据的使用范围
- 选择性启用功能

## Cookie 和本地存储技术

### 使用的技术

- **WebExtensions Storage API**：存储用户数据和设置
- **IndexedDB**：存储大型文档和媒体文件
- **Local Storage**：缓存临时数据

### 用途说明

- 保存用户文档和编辑历史
- 存储个性化设置和偏好
- 缓存数据以提升性能
- 记住用户的操作状态

### 用户控制

- 可通过扩展设置清理数据
- 可通过 Firefox 设置管理存储
- 卸载扩展自动清理所有数据

## 数据保留政策

### 本地数据

- **保留期限**：直到用户主动删除或卸载扩展
- **自动清理**：卸载扩展时自动清理所有本地数据
- **用户控制**：用户可随时删除特定数据

### 云端数据（如启用）

- **保留期限**：根据用户设置，最长不超过 2 年
- **自动删除**：超过保留期限自动删除
- **用户删除**：用户可随时删除云端数据

### 技术日志

- **保留期限**：最长 90 天
- **匿名化处理**：不包含个人身份信息
- **自动删除**：超期自动删除

## 儿童隐私保护

本扩展不专门面向 16 岁以下的未成年人。我们不会故意收集未成年人的个人信息。如果发现收集了此类信息，我们将：

- 立即停止收集
- 尽快删除相关数据
- 通知监护人（如可能）
- 改进防护措施

## 跨境数据传输

如果您位于欧盟或其他具有严格数据保护法律的地区：

- 我们会确保数据传输符合当地法律要求
- 使用适当的保护措施（如标准合同条款）
- 仅与提供充分保护的第三方合作
- 用户可选择仅使用本地存储功能

## 隐私政策更新

### 更新通知

- 重大更改前至少提前 30 天通知
- 在扩展中显示更新提醒
- 通过官方渠道发布公告
- 更新版本号和生效日期

### 用户选择

- 用户可选择接受或拒绝更新
- 拒绝重大更改可能影响部分功能
- 提供详细的更改说明

## 联系我们

如果您对隐私政策有任何疑问、建议或投诉：

**主要联系方式：**

- 邮箱：<EMAIL>
- GitHub Issues：https://github.com/zillionare/marktwain/issues

**其他联系方式：**

- 官方网站：https://zillionare.github.io/marktwain
- 社区讨论：https://github.com/zillionare/marktwain/discussions

**响应承诺：**

- 我们将在 7 个工作日内回复您的询问
- 对于紧急隐私问题，我们将在 24 小时内响应

## 法律依据和管辖

本隐私政策的制定和执行基于：

- 中华人民共和国相关法律法规
- 欧盟通用数据保护条例（GDPR）
- Mozilla 附加组件政策
- 其他适用的国际数据保护标准

如有争议，优先通过友好协商解决。协商不成的，提交至有管辖权的法院处理。

---

**我们承诺持续改进隐私保护措施，确保您的数据安全。感谢您对 MarkTwain 的信任！**
