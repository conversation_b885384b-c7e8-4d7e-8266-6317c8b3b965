<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MarkT<PERSON> 扩展图标生成器</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        margin: 0;
        padding: 20px;
        background: #f5f5f5;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        padding: 30px;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      }

      h1 {
        text-align: center;
        color: #333;
        margin-bottom: 40px;
      }

      .icon-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
        margin-bottom: 40px;
      }

      .icon-card {
        border: 2px solid #e0e0e0;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        background: #fafafa;
      }

      .icon-card h3 {
        margin-top: 0;
        color: #555;
      }

      .icon-container {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 20px 0;
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      /* 16x16 图标 */
      .icon-16 {
        width: 16px;
        height: 16px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 3px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .icon-16::before {
        content: 'M';
        color: white;
        font-size: 10px;
        font-weight: bold;
        font-family: 'Georgia', serif;
      }

      /* 48x48 图标 */
      .icon-48 {
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 8px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      }

      .icon-48::before {
        content: 'M2';
        color: white;
        font-size: 18px;
        font-weight: bold;
        font-family: 'Georgia', serif;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      }

      /* 128x128 图标 */
      .icon-128 {
        width: 128px;
        height: 128px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
      }

      .icon-128::before {
        content: 'M2';
        color: white;
        font-size: 48px;
        font-weight: bold;
        font-family: 'Georgia', serif;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        margin-bottom: 4px;
      }

      .icon-128::after {
        content: 'MarkTwain';
        color: rgba(255, 255, 255, 0.9);
        font-size: 12px;
        font-weight: 500;
        font-family: -apple-system, BlinkMacSystemFont, sans-serif;
        letter-spacing: 0.5px;
      }

      /* 256x256 图标 (用于高分辨率显示) */
      .icon-256 {
        width: 256px;
        height: 256px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 40px;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        box-shadow: 0 16px 48px rgba(102, 126, 234, 0.4);
      }

      .icon-256::before {
        content: 'M2';
        color: white;
        font-size: 96px;
        font-weight: bold;
        font-family: 'Georgia', serif;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        margin-bottom: 8px;
      }

      .icon-256::after {
        content: 'MarkTwain';
        color: rgba(255, 255, 255, 0.95);
        font-size: 24px;
        font-weight: 500;
        font-family: -apple-system, BlinkMacSystemFont, sans-serif;
        letter-spacing: 1px;
      }

      .size-info {
        margin-top: 15px;
        color: #666;
        font-size: 14px;
      }

      .usage-info {
        background: #e3f2fd;
        color: #1565c0;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        margin-top: 10px;
      }

      .instructions {
        background: #fff3e0;
        border: 1px solid #ffb74d;
        border-radius: 8px;
        padding: 20px;
        margin-top: 30px;
      }

      .instructions h3 {
        color: #e65100;
        margin-top: 0;
      }

      .instructions ol {
        color: #bf360c;
      }

      .instructions li {
        margin-bottom: 8px;
      }

      /* 放大显示区域 */
      .enlarged-view {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 40px;
        margin-top: 40px;
        padding-top: 40px;
        border-top: 2px solid #e0e0e0;
      }

      .enlarged-icon {
        text-align: center;
      }

      .enlarged-icon h4 {
        color: #333;
        margin-bottom: 20px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>MarkTwain 浏览器扩展图标</h1>

      <div class="icon-grid">
        <div class="icon-card">
          <h3>16×16 工具栏图标</h3>
          <div class="icon-container">
            <div class="icon-16"></div>
          </div>
          <div class="size-info">16×16 像素</div>
          <div class="usage-info">Chrome: 工具栏显示</div>
        </div>

        <div class="icon-card">
          <h3>48×48 扩展管理图标</h3>
          <div class="icon-container">
            <div class="icon-48"></div>
          </div>
          <div class="size-info">48×48 像素</div>
          <div class="usage-info">Chrome/Firefox: 扩展管理页面</div>
        </div>

        <div class="icon-card">
          <h3>128×128 商店图标</h3>
          <div class="icon-container">
            <div class="icon-128"></div>
          </div>
          <div class="size-info">128×128 像素</div>
          <div class="usage-info">Chrome/Firefox: 商店展示</div>
        </div>
      </div>

      <div class="enlarged-view">
        <div class="enlarged-icon">
          <h4>128×128 商店图标 (放大显示)</h4>
          <div class="icon-container">
            <div class="icon-128"></div>
          </div>
        </div>

        <div class="enlarged-icon">
          <h4>256×256 高分辨率图标</h4>
          <div class="icon-container">
            <div class="icon-256"></div>
          </div>
        </div>
      </div>

      <div class="instructions">
        <h3>📸 截图说明</h3>
        <ol>
          <li>使用浏览器的开发者工具或截图工具</li>
          <li>分别截取每个图标的精确尺寸</li>
          <li>确保背景透明（PNG格式）</li>
          <li>
            保存为对应的文件名：
            <ul>
              <li>icon-16.png (16×16)</li>
              <li>icon-48.png (48×48)</li>
              <li>icon-128.png (128×128)</li>
              <li>icon-256.png (256×256)</li>
            </ul>
          </li>
          <li>将文件分别复制到 material/chrome/icons/ 和 material/firefox/icons/ 目录</li>
        </ol>
      </div>
    </div>
  </body>
</html>
