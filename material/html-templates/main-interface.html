<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MarkTwain - 主界面展示</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: #f8fafc;
        width: 1280px;
        height: 800px;
        overflow: hidden;
      }

      .app-container {
        display: flex;
        height: 100vh;
        background: white;
      }

      /* 侧边栏 */
      .sidebar {
        width: 280px;
        background: #1e293b;
        color: white;
        display: flex;
        flex-direction: column;
      }

      .sidebar-header {
        padding: 20px;
        border-bottom: 1px solid #334155;
      }

      .logo {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 20px;
        font-weight: bold;
      }

      .logo-icon {
        width: 32px;
        height: 32px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 14px;
        font-weight: bold;
      }

      .sidebar-nav {
        flex: 1;
        padding: 20px 0;
      }

      .nav-item {
        padding: 12px 20px;
        cursor: pointer;
        transition: background 0.2s;
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .nav-item:hover {
        background: #334155;
      }

      .nav-item.active {
        background: #3b82f6;
      }

      .nav-icon {
        width: 20px;
        height: 20px;
        background: currentColor;
        border-radius: 2px;
        opacity: 0.8;
      }

      /* 主内容区 */
      .main-content {
        flex: 1;
        display: flex;
        flex-direction: column;
      }

      .header {
        height: 60px;
        background: white;
        border-bottom: 1px solid #e2e8f0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 24px;
      }

      .header-title {
        font-size: 18px;
        font-weight: 600;
        color: #1e293b;
      }

      .header-actions {
        display: flex;
        gap: 12px;
      }

      .btn {
        padding: 8px 16px;
        border-radius: 6px;
        border: none;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.2s;
      }

      .btn-primary {
        background: #3b82f6;
        color: white;
      }

      .btn-primary:hover {
        background: #2563eb;
      }

      .btn-secondary {
        background: #f1f5f9;
        color: #475569;
        border: 1px solid #e2e8f0;
      }

      .btn-secondary:hover {
        background: #e2e8f0;
      }

      /* 编辑器区域 */
      .editor-container {
        flex: 1;
        display: flex;
      }

      .editor-panel {
        flex: 1;
        display: flex;
        flex-direction: column;
        border-right: 1px solid #e2e8f0;
      }

      .panel-header {
        height: 40px;
        background: #f8fafc;
        border-bottom: 1px solid #e2e8f0;
        display: flex;
        align-items: center;
        padding: 0 16px;
        font-size: 14px;
        font-weight: 500;
        color: #475569;
      }

      .editor-content {
        flex: 1;
        padding: 20px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 14px;
        line-height: 1.6;
        color: #1e293b;
        background: #fefefe;
      }

      .preview-panel {
        flex: 1;
        display: flex;
        flex-direction: column;
      }

      .preview-content {
        flex: 1;
        padding: 20px;
        background: white;
        overflow-y: auto;
      }

      /* Markdown 样式 */
      .markdown-content h1 {
        font-size: 28px;
        font-weight: bold;
        color: #1e293b;
        margin-bottom: 16px;
        border-bottom: 2px solid #e2e8f0;
        padding-bottom: 8px;
      }

      .markdown-content h2 {
        font-size: 22px;
        font-weight: 600;
        color: #334155;
        margin: 24px 0 12px 0;
      }

      .markdown-content p {
        margin-bottom: 16px;
        color: #475569;
        line-height: 1.7;
      }

      .markdown-content ul {
        margin-bottom: 16px;
        padding-left: 24px;
      }

      .markdown-content li {
        margin-bottom: 8px;
        color: #475569;
      }

      .markdown-content code {
        background: #f1f5f9;
        padding: 2px 6px;
        border-radius: 4px;
        font-family: 'Monaco', monospace;
        font-size: 13px;
        color: #e11d48;
      }

      .markdown-content pre {
        background: #1e293b;
        color: #e2e8f0;
        padding: 16px;
        border-radius: 8px;
        margin-bottom: 16px;
        overflow-x: auto;
      }

      /* 工具栏 */
      .toolbar {
        height: 50px;
        background: #f8fafc;
        border-bottom: 1px solid #e2e8f0;
        display: flex;
        align-items: center;
        padding: 0 16px;
        gap: 8px;
      }

      .toolbar-btn {
        width: 32px;
        height: 32px;
        border: none;
        background: transparent;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background 0.2s;
      }

      .toolbar-btn:hover {
        background: #e2e8f0;
      }

      .toolbar-separator {
        width: 1px;
        height: 20px;
        background: #cbd5e1;
        margin: 0 4px;
      }
    </style>
  </head>
  <body>
    <div class="app-container">
      <!-- 侧边栏 -->
      <div class="sidebar">
        <div class="sidebar-header">
          <div class="logo">
            <div class="logo-icon">M2</div>
            <span>MarkTwain</span>
          </div>
        </div>
        <nav class="sidebar-nav">
          <div class="nav-item active">
            <div class="nav-icon"></div>
            <span>编辑器</span>
          </div>
          <div class="nav-item">
            <div class="nav-icon"></div>
            <span>文档管理</span>
          </div>
          <div class="nav-item">
            <div class="nav-icon"></div>
            <span>发布平台</span>
          </div>
          <div class="nav-item">
            <div class="nav-icon"></div>
            <span>主题设置</span>
          </div>
          <div class="nav-item">
            <div class="nav-icon"></div>
            <span>图床配置</span>
          </div>
          <div class="nav-item">
            <div class="nav-icon"></div>
            <span>AI 助手</span>
          </div>
        </nav>
      </div>

      <!-- 主内容区 -->
      <div class="main-content">
        <header class="header">
          <h1 class="header-title">Markdown 编辑器</h1>
          <div class="header-actions">
            <button class="btn btn-secondary">导入</button>
            <button class="btn btn-secondary">导出</button>
            <button class="btn btn-primary">发布到微信</button>
          </div>
        </header>

        <div class="toolbar">
          <button class="toolbar-btn" title="粗体">B</button>
          <button class="toolbar-btn" title="斜体">I</button>
          <button class="toolbar-btn" title="下划线">U</button>
          <div class="toolbar-separator"></div>
          <button class="toolbar-btn" title="标题">H</button>
          <button class="toolbar-btn" title="链接">🔗</button>
          <button class="toolbar-btn" title="图片">🖼️</button>
          <div class="toolbar-separator"></div>
          <button class="toolbar-btn" title="代码块">{ }</button>
          <button class="toolbar-btn" title="表格">📊</button>
          <button class="toolbar-btn" title="公式">∑</button>
        </div>

        <div class="editor-container">
          <div class="editor-panel">
            <div class="panel-header">Markdown 源码</div>
            <div class="editor-content">
              # MarkTwain - 一处编辑，处处发布 ## 功能特性 - **多平台发布**: 支持微信公众号、知乎、CSDN等平台 -
              **实时预览**: 所见即所得的编辑体验 - **主题定制**: 多种内置主题，支持自定义样式 - **图床支持**:
              自动上传图片到图床 - **AI 助手**: 智能内容优化和生成 ## 代码示例 ```javascript // 发布到微信公众号 const
              publisher = new WechatPublisher({ token: 'your-token', theme: 'github' }); await
              publisher.publish(markdown); ``` ## 数学公式 当 $a \ne 0$ 时，方程 $ax^2 + bx + c = 0$ 的解为： $$x =
              \frac{-b \pm \sqrt{b^2-4ac}}{2a}$$
            </div>
          </div>

          <div class="preview-panel">
            <div class="panel-header">实时预览</div>
            <div class="preview-content">
              <div class="markdown-content">
                <h1>MarkTwain - 一处编辑，处处发布</h1>

                <h2>功能特性</h2>
                <ul>
                  <li><strong>多平台发布</strong>: 支持微信公众号、知乎、CSDN等平台</li>
                  <li><strong>实时预览</strong>: 所见即所得的编辑体验</li>
                  <li><strong>主题定制</strong>: 多种内置主题，支持自定义样式</li>
                  <li><strong>图床支持</strong>: 自动上传图片到图床</li>
                  <li><strong>AI 助手</strong>: 智能内容优化和生成</li>
                </ul>

                <h2>代码示例</h2>
                <pre><code>// 发布到微信公众号
const publisher = new WechatPublisher({
  token: 'your-token',
  theme: 'github'
});

await publisher.publish(markdown);</code></pre>

                <h2>数学公式</h2>
                <p>当 <code>a ≠ 0</code> 时，方程 <code>ax² + bx + c = 0</code> 的解为：</p>
                <p style="text-align: center; font-size: 18px; margin: 20px 0">
                  <em>x = (-b ± √(b²-4ac)) / 2a</em>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
