<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MarkTwain - 多平台发布功能</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: #f8fafc;
        width: 1280px;
        height: 800px;
        overflow: hidden;
      }

      .app-container {
        display: flex;
        height: 100vh;
        background: white;
      }

      /* 左侧编辑区 */
      .editor-section {
        width: 400px;
        background: white;
        border-right: 1px solid #e2e8f0;
        display: flex;
        flex-direction: column;
      }

      .section-header {
        height: 60px;
        background: #1e293b;
        color: white;
        display: flex;
        align-items: center;
        padding: 0 20px;
        font-size: 16px;
        font-weight: 600;
      }

      .editor-content {
        flex: 1;
        padding: 20px;
        font-family: 'Monaco', 'Menlo', monospace;
        font-size: 13px;
        line-height: 1.6;
        color: #1e293b;
        background: #fefefe;
        overflow-y: auto;
      }

      /* 右侧发布区 */
      .publish-section {
        flex: 1;
        display: flex;
        flex-direction: column;
      }

      .publish-header {
        height: 60px;
        background: #3b82f6;
        color: white;
        display: flex;
        align-items: center;
        justify-content: between;
        padding: 0 24px;
      }

      .publish-title {
        font-size: 16px;
        font-weight: 600;
      }

      .publish-actions {
        margin-left: auto;
        display: flex;
        gap: 12px;
      }

      .btn {
        padding: 8px 16px;
        border-radius: 6px;
        border: none;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.2s;
      }

      .btn-white {
        background: white;
        color: #3b82f6;
      }

      .btn-white:hover {
        background: #f1f5f9;
      }

      /* 平台选择区 */
      .platform-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
        padding: 24px;
        background: #f8fafc;
      }

      .platform-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        text-align: center;
        cursor: pointer;
        transition: all 0.2s;
        border: 2px solid transparent;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .platform-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      .platform-card.selected {
        border-color: #3b82f6;
        background: #eff6ff;
      }

      .platform-icon {
        width: 48px;
        height: 48px;
        margin: 0 auto 12px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
      }

      .wechat {
        background: #07c160;
      }
      .zhihu {
        background: #0084ff;
      }
      .csdn {
        background: #fc5531;
      }
      .juejin {
        background: #1e80ff;
      }
      .github {
        background: #24292e;
      }
      .medium {
        background: #00ab6c;

      .platform-name {
        font-size: 16px;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 4px;
      }

      .platform-status {
        font-size: 12px;
        color: #64748b;
      }

      .status-ready {
        color: #059669;
      }
      .status-config {
        color: #d97706;

      /* 发布预览区 */
      .preview-area {
        flex: 1;
        padding: 24px;
        background: white;
        overflow-y: auto;
      }

      .preview-header {
        display: flex;
        align-items: center;
        justify-content: between;
        margin-bottom: 20px;
      }

      .preview-title {
        font-size: 18px;
        font-weight: 600;
        color: #1e293b;
      }

      .theme-selector {
        display: flex;
        gap: 8px;
        margin-left: auto;
      }

      .theme-btn {
        width: 24px;
        height: 24px;
        border-radius: 4px;
        border: 2px solid #e2e8f0;
        cursor: pointer;
        transition: all 0.2s;
      }

      .theme-btn.active {
        border-color: #3b82f6;
      }

      .theme-github {
        background: #f6f8fa;
      }
      .theme-juejin {
        background: #1e1e1e;
      }
      .theme-wechat {
        background: #2ecc71;

      /* 预览内容 */
      .preview-content {
        background: #fefefe;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 24px;
        min-height: 400px;
      }

      .article-title {
        font-size: 24px;
        font-weight: bold;
        color: #1e293b;
        margin-bottom: 16px;
        text-align: center;
      }

      .article-meta {
        text-align: center;
        color: #64748b;
        font-size: 14px;
        margin-bottom: 24px;
        padding-bottom: 16px;
        border-bottom: 1px solid #e2e8f0;
      }

      .article-content h2 {
        font-size: 20px;
        font-weight: 600;
        color: #334155;
        margin: 20px 0 12px 0;
      }

      .article-content p {
        margin-bottom: 16px;
        color: #475569;
        line-height: 1.7;
      }

      .article-content ul {
        margin-bottom: 16px;
        padding-left: 24px;
      }

      .article-content li {
        margin-bottom: 8px;
        color: #475569;
      }

      .highlight-box {
        background: #eff6ff;
        border-left: 4px solid #3b82f6;
        padding: 16px;
        margin: 16px 0;
        border-radius: 0 8px 8px 0;
      }

      /* 发布按钮区 */
      .publish-footer {
        padding: 20px 24px;
        background: #f8fafc;
        border-top: 1px solid #e2e8f0;
        display: flex;
        justify-content: between;
        align-items: center;
      }

      .publish-info {
        color: #64748b;
        font-size: 14px;
      }

      .publish-btn {
        background: #3b82f6;
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        border: none;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
      }

      .publish-btn:hover {
        background: #2563eb;
        transform: translateY(-1px);
      }

      .publish-btn:disabled {
        background: #94a3b8;
        cursor: not-allowed;
        transform: none;
      }
    </style>
  </head>
  <body>
    <div class="app-container">
      <!-- 左侧编辑区 -->
      <div class="editor-section">
        <div class="section-header">📝 Markdown 源码</div>
        <div class="editor-content">
          # 如何使用 MarkTwain 提升写作效率 ## 简介 MarkTwain 是一款强大的 Markdown 编辑器，支持一处编辑，多平台发布。
          ## 核心功能 ### 1. 多平台发布 - 微信公众号 - 知乎专栏 - CSDN博客 - 掘金社区 ### 2. 智能格式转换 -
          自动适配平台样式 - 代码块截图上传 - 数学公式图片化 ### 3. 图床集成 - GitHub 图床 - 七牛云存储 - 腾讯云 COS >
          **提示**: 使用 MarkTwain 可以大大提升内容创作效率！ ## 使用步骤 1. 编写 Markdown 内容 2. 选择发布平台 3.
          预览格式效果 4. 一键发布内容 让我们开始高效的内容创作之旅吧！
        </div>
      </div>

      <!-- 右侧发布区 -->
      <div class="publish-section">
        <div class="publish-header">
          <div class="publish-title">🚀 多平台发布</div>
          <div class="publish-actions">
            <button class="btn btn-white">配置平台</button>
          </div>
        </div>

        <!-- 平台选择 -->
        <div class="platform-grid">
          <div class="platform-card selected">
            <div class="platform-icon wechat">📱</div>
            <div class="platform-name">微信公众号</div>
            <div class="platform-status status-ready">✓ 已配置</div>
          </div>

          <div class="platform-card">
            <div class="platform-icon zhihu">🔵</div>
            <div class="platform-name">知乎专栏</div>
            <div class="platform-status status-ready">✓ 已配置</div>
          </div>

          <div class="platform-card">
            <div class="platform-icon csdn">🔴</div>
            <div class="platform-name">CSDN</div>
            <div class="platform-status status-config">⚠ 需配置</div>
          </div>

          <div class="platform-card">
            <div class="platform-icon juejin">💎</div>
            <div class="platform-name">掘金</div>
            <div class="platform-status status-ready">✓ 已配置</div>
          </div>

          <div class="platform-card">
            <div class="platform-icon github">⚫</div>
            <div class="platform-name">GitHub</div>
            <div class="platform-status status-ready">✓ 已配置</div>
          </div>

          <div class="platform-card">
            <div class="platform-icon medium">🟢</div>
            <div class="platform-name">Medium</div>
            <div class="platform-status status-config">⚠ 需配置</div>
          </div>
        </div>

        <!-- 预览区 -->
        <div class="preview-area">
          <div class="preview-header">
            <div class="preview-title">📱 微信公众号预览</div>
            <div class="theme-selector">
              <div class="theme-btn theme-wechat active" title="微信主题"></div>
              <div class="theme-btn theme-github" title="GitHub主题"></div>
              <div class="theme-btn theme-juejin" title="掘金主题"></div>
            </div>
          </div>

          <div class="preview-content">
            <h1 class="article-title">如何使用 MarkTwain 提升写作效率</h1>
            <div class="article-meta">作者：MarkTwain用户 | 发布时间：2024-01-15</div>

            <div class="article-content">
              <h2>简介</h2>
              <p>MarkTwain 是一款强大的 Markdown 编辑器，支持一处编辑，多平台发布。</p>

              <h2>核心功能</h2>

              <div class="highlight-box">
                <strong>💡 核心优势</strong><br />
                使用 MarkTwain 可以大大提升内容创作效率！
              </div>

              <p><strong>1. 多平台发布</strong></p>
              <ul>
                <li>微信公众号</li>
                <li>知乎专栏</li>
                <li>CSDN博客</li>
                <li>掘金社区</li>
              </ul>

              <p><strong>2. 智能格式转换</strong></p>
              <ul>
                <li>自动适配平台样式</li>
                <li>代码块截图上传</li>
                <li>数学公式图片化</li>
              </ul>

              <p><strong>3. 图床集成</strong></p>
              <ul>
                <li>GitHub 图床</li>
                <li>七牛云存储</li>
                <li>腾讯云 COS</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- 发布按钮 -->
        <div class="publish-footer">
          <div class="publish-info">已选择 1 个平台 | 预计发布时间: 2分钟</div>
          <button class="publish-btn">🚀 发布到微信公众号</button>
        </div>
      </div>
    </div>
  </body>
</html>
