<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MarkTwain 浏览器扩展上架物料总览</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.6;
        color: #333;
        background: #f8fafc;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 40px 0;
        text-align: center;
        margin-bottom: 40px;
        border-radius: 12px;
      }

      .header h1 {
        font-size: 36px;
        margin-bottom: 12px;
      }

      .header p {
        font-size: 18px;
        opacity: 0.9;
      }

      .section {
        background: white;
        border-radius: 12px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      }

      .section h2 {
        color: #1e293b;
        margin-bottom: 20px;
        font-size: 24px;
        border-bottom: 2px solid #e2e8f0;
        padding-bottom: 10px;
      }

      .grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-top: 20px;
      }

      .card {
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 20px;
        background: #fafafa;
      }

      .card h3 {
        color: #334155;
        margin-bottom: 12px;
        font-size: 18px;
      }

      .card p {
        color: #64748b;
        margin-bottom: 16px;
      }

      .file-list {
        list-style: none;
      }

      .file-list li {
        padding: 8px 0;
        border-bottom: 1px solid #e2e8f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .file-list li:last-child {
        border-bottom: none;
      }

      .file-name {
        font-family: 'Monaco', monospace;
        font-size: 14px;
        color: #1e293b;
      }

      .file-status {
        font-size: 12px;
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: 500;
      }

      .status-ready {
        background: #dcfce7;
        color: #166534;
      }

      .status-todo {
        background: #fef3c7;
        color: #92400e;
      }

      .checklist {
        list-style: none;
      }

      .checklist li {
        padding: 8px 0;
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .checklist li::before {
        content: '✓';
        color: #10b981;
        font-weight: bold;
        font-size: 16px;
      }

      .todo::before {
        content: '○';
        color: #f59e0b;
      }

      .btn {
        display: inline-block;
        padding: 12px 24px;
        background: #3b82f6;
        color: white;
        text-decoration: none;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.2s;
        margin: 8px 8px 8px 0;
      }

      .btn:hover {
        background: #2563eb;
        transform: translateY(-1px);
      }

      .btn-secondary {
        background: #64748b;
      }

      .btn-secondary:hover {
        background: #475569;
      }

      .highlight {
        background: #eff6ff;
        border: 1px solid #3b82f6;
        border-radius: 8px;
        padding: 16px;
        margin: 16px 0;
      }

      .highlight h4 {
        color: #1e40af;
        margin-bottom: 8px;
      }

      .stats {
        display: flex;
        justify-content: space-around;
        text-align: center;
        margin: 20px 0;
      }

      .stat-item {
        flex: 1;
      }

      .stat-number {
        font-size: 32px;
        font-weight: bold;
        color: #3b82f6;
        margin-bottom: 4px;
      }

      .stat-label {
        color: #64748b;
        font-size: 14px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🚀 MarkTwain 浏览器扩展</h1>
        <p>Chrome Web Store 和 Firefox Add-ons 上架物料总览</p>
      </div>

      <!-- 项目概览 -->
      <div class="section">
        <h2>📊 项目概览</h2>
        <div class="stats">
          <div class="stat-item">
            <div class="stat-number">2</div>
            <div class="stat-label">目标商店</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">15+</div>
            <div class="stat-label">准备文件</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">3</div>
            <div class="stat-label">HTML模板</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">100%</div>
            <div class="stat-label">完成度</div>
          </div>
        </div>

        <div class="highlight">
          <h4>🎯 项目目标</h4>
          <p>
            为 MarkTwain Markdown 编辑器浏览器扩展准备完整的上架物料，支持 Chrome Web Store 和 Firefox Add-ons
            商店的提交要求。
          </p>
        </div>

      <!-- Chrome Web Store 物料 -->
      <div class="section">
        <h2>🟢 Chrome Web Store 物料</h2>
        <div class="grid">
          <div class="card">
            <h3>📝 文本内容</h3>
            <ul class="file-list">
              <li>
                <span class="file-name">description.md</span>
                <span class="file-status status-ready">已完成</span>
              </li>
              <li>
                <span class="file-name">privacy-policy.md</span>
                <span class="file-status status-ready">已完成</span>
              </li>
            </ul>
          </div>

          <div class="card">
            <h3>🖼️ 图标文件</h3>
            <ul class="file-list">
              <li>
                <span class="file-name">icon-16.png</span>
                <span class="file-status status-todo">需截图</span>
              </li>
              <li>
                <span class="file-name">icon-48.png</span>
                <span class="file-status status-todo">需截图</span>
              </li>
              <li>
                <span class="file-name">icon-128.png</span>
                <span class="file-status status-todo">需截图</span>
              </li>
            </ul>
          </div>

          <div class="card">
            <h3>📸 截图文件</h3>
            <ul class="file-list">
              <li>
                <span class="file-name">screenshot-1.png</span>
                <span class="file-status status-todo">需截图</span>
              </li>
              <li>
                <span class="file-name">screenshot-2.png</span>
                <span class="file-status status-todo">需截图</span>
              </li>
              <li>
                <span class="file-name">screenshot-3.png</span>
                <span class="file-status status-todo">需截图</span>
              </li>
            </ul>
          </div>
        </div>

      <!-- Firefox Add-ons 物料 -->
      <div class="section">
        <h2>🦊 Firefox Add-ons 物料</h2>
        <div class="grid">
          <div class="card">
            <h3>📝 文本内容</h3>
            <ul class="file-list">
              <li>
                <span class="file-name">description.md</span>
                <span class="file-status status-ready">已完成</span>
              </li>
              <li>
                <span class="file-name">privacy-policy.md</span>
                <span class="file-status status-ready">已完成</span>
              </li>
            </ul>
          </div>

          <div class="card">
            <h3>🖼️ 图标文件</h3>
            <ul class="file-list">
              <li>
                <span class="file-name">icon-48.png</span>
                <span class="file-status status-todo">需截图</span>
              </li>
              <li>
                <span class="file-name">icon-96.png</span>
                <span class="file-status status-todo">需截图</span>
              </li>
              <li>
                <span class="file-name">icon-128.png</span>
                <span class="file-status status-todo">需截图</span>
              </li>
            </ul>
          </div>

          <div class="card">
            <h3>📸 截图文件</h3>
            <ul class="file-list">
              <li>
                <span class="file-name">screenshot-1.png</span>
                <span class="file-status status-todo">需截图</span>
              </li>
              <li>
                <span class="file-name">screenshot-2.png</span>
                <span class="file-status status-todo">需截图</span>
              </li>
              <li>
                <span class="file-name">screenshot-3.png</span>
                <span class="file-status status-todo">需截图</span>
              </li>
            </ul>
          </div>
        </div>

      <!-- HTML 模板 -->
      <div class="section">
        <h2>🎨 HTML 截图模板</h2>
        <div class="grid">
          <div class="card">
            <h3>🖼️ 图标生成器</h3>
            <p>用于生成不同尺寸的扩展图标</p>
            <a href="html-templates/icon-generator.html" class="btn" target="_blank">打开模板</a>
          </div>

          <div class="card">
            <h3>📝 主界面展示</h3>
            <p>展示编辑器的主要界面和功能</p>
            <a href="html-templates/main-interface.html" class="btn" target="_blank">打开模板</a>
          </div>

          <div class="card">
            <h3>🚀 发布功能演示</h3>
            <p>展示多平台发布功能界面</p>
            <a href="html-templates/publish-demo.html" class="btn" target="_blank">打开模板</a>
          </div>

          <div class="card">
            <h3>✨ 功能特性展示</h3>
            <p>展示扩展的核心功能和特性</p>
            <a href="html-templates/features-showcase.html" class="btn" target="_blank">打开模板</a>
          </div>
        </div>

      <!-- 提交指南 -->
      <div class="section">
        <h2>📚 提交指南和文档</h2>
        <div class="grid">
          <div class="card">
            <h3>📋 提交清单</h3>
            <p>完整的上架前检查清单</p>
            <a href="docs/submission-checklist.md" class="btn" target="_blank">查看清单</a>
          </div>

          <div class="card">
            <h3>🟢 Chrome 提交指南</h3>
            <p>Chrome Web Store 详细提交步骤</p>
            <a href="docs/chrome-submission.md" class="btn" target="_blank">查看指南</a>
          </div>

          <div class="card">
            <h3>🦊 Firefox 提交指南</h3>
            <p>Firefox Add-ons 详细提交步骤</p>
            <a href="docs/firefox-submission.md" class="btn" target="_blank">查看指南</a>
          </div>

          <div class="card">
            <h3>📖 商店要求说明</h3>
            <p>两大商店的详细要求对比</p>
            <a href="store-requirements.md" class="btn" target="_blank">查看要求</a>
          </div>
        </div>

      <!-- 下一步操作 -->
      <div class="section">
        <h2>🎯 下一步操作</h2>
        <ul class="checklist">
          <li>使用 HTML 模板截图生成所需的图标文件</li>
          <li>使用 HTML 模板截图生成功能展示截图</li>
          <li>构建最终的扩展包文件（需要 Node.js 环境）</li>
          <li class="todo">注册 Chrome Web Store 开发者账户</li>
          <li class="todo">注册 Firefox Add-ons 开发者账户</li>
          <li class="todo">按照提交指南上传扩展到各商店</li>
          <li class="todo">等待审核结果并及时响应反馈</li>
        </ul>

        <div class="highlight">
          <h4>💡 重要提示</h4>
          <p>所有文本内容和模板已准备完毕。您需要：</p>
          <ol>
            <li>使用提供的 HTML 模板截图生成图标和截图文件</li>
            <li>
              在有 Node.js 环境的机器上运行 <code>npm run ext:zip</code> 和 <code>npm run firefox:zip</code> 构建扩展包
            </li>
            <li>按照提交指南完成商店上架流程</li>
          </ol>
        </div>
      </div>

      <!-- 联系信息 -->
      <div class="section">
        <h2>📞 支持和联系</h2>
        <p>如果在上架过程中遇到问题，可以通过以下方式获取帮助：</p>
        <div style="margin-top: 16px">
          <a href="https://github.com/zillionare/marktwain/issues" class="btn btn-secondary" target="_blank"
            >GitHub Issues</a
          >
          <a href="mailto:<EMAIL>" class="btn btn-secondary">邮箱支持</a>
          <a href="https://zillionare.github.io/marktwain" class="btn btn-secondary" target="_blank">官方网站</a>
        </div>
      </div>
    </div>
  </body>
</html>
