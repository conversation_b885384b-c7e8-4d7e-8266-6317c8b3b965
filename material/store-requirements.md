# 浏览器扩展商店要求详细说明

## Chrome Web Store 要求

### 图标要求

- **16x16**: 工具栏图标，PNG格式，透明背景
- **48x48**: 扩展管理页面图标，PNG格式，透明背景
- **128x128**: Chrome Web Store展示图标，PNG格式，透明背景
- **要求**: 所有图标必须是正方形，清晰可辨，避免使用过多细节

### 截图要求

- **数量**: 最少1张，最多5张
- **尺寸**:
  - 推荐: 1280x800像素 (16:10比例)
  - 备选: 640x400像素 (16:10比例)
- **格式**: PNG或JPEG
- **内容**: 展示扩展的主要功能和用户界面

### 推广图片 (可选)

- **小型推广图片**: 440x280像素
- **大型推广图片**: 1400x560像素
- **截图推广图片**: 640x400像素
- **格式**: PNG或JPEG

### 文本要求

- **名称**: 最多45个字符
- **简短描述**: 最多132个字符
- **详细描述**: 最多16,000个字符
- **支持的语言**: 支持多语言本地化

### 其他要求

- **隐私政策**: 如果扩展收集用户数据，必须提供隐私政策链接
- **权限说明**: 清楚解释为什么需要特定权限
- **分类**: 选择合适的扩展分类

## Firefox Add-ons (AMO) 要求

### 图标要求

- **48x48**: 基础图标，PNG格式，透明背景
- **96x96**: 高分辨率图标，PNG格式，透明背景
- **128x128**: AMO商店展示图标，PNG格式，透明背景
- **要求**: 图标应该简洁明了，在小尺寸下仍然清晰可辨

### 截图要求

- **数量**: 最多10张
- **尺寸**: 建议1280x800像素或更高分辨率
- **格式**: PNG, JPEG, 或 GIF
- **内容**: 展示扩展的核心功能，按重要性排序

### 文本要求

- **名称**: 建议50个字符以内
- **摘要**: 最多250个字符
- **详细描述**: 无字符限制，支持Markdown格式
- **标签**: 最多20个标签，帮助用户发现扩展

### 分类要求

- **主分类**: 选择最符合扩展功能的主要分类
- **次分类**: 可选择次要分类
- **平台**: 指定支持的Firefox版本

### 隐私和安全要求

- **隐私政策**: 如果收集任何用户数据，必须提供隐私政策
- **权限解释**: 在描述中说明为什么需要特定权限
- **源代码**: 如果使用了混淆代码，可能需要提供源代码

## 通用最佳实践

### 图标设计

- 使用简洁的设计，避免过多细节
- 确保在不同尺寸下都清晰可见
- 使用品牌一致的颜色和风格
- 避免使用文字，除非是品牌标识的一部分

### 截图策略

- 第一张截图最重要，展示主要功能
- 按功能重要性排序
- 使用真实的使用场景
- 添加简洁的说明文字（如果商店支持）
- 确保截图清晰，避免模糊

### 描述写作

- 开头直接说明扩展的主要价值
- 使用项目符号列出主要功能
- 包含使用场景和目标用户
- 说明扩展的独特优势
- 提供支持联系方式

### 本地化考虑

- 考虑提供多语言版本
- 确保图标和截图适用于不同文化
- 翻译描述文本时保持核心信息一致

## 审核注意事项

### Chrome Web Store

- 审核通常需要几天到几周时间
- 重点检查权限使用的合理性
- 确保扩展功能与描述一致
- 避免误导性的描述或截图

### Firefox Add-ons

- 自动审核和人工审核相结合
- 重点检查安全性和隐私保护
- 确保遵循Mozilla的附加组件政策
- 提供清晰的源代码（如果需要）

## 常见拒绝原因

1. **权限过度**: 请求了不必要的权限
2. **描述不准确**: 功能描述与实际不符
3. **图标质量差**: 图标模糊或不专业
4. **隐私政策缺失**: 收集数据但未提供隐私政策
5. **功能重复**: 与现有扩展功能高度重复
6. **违反政策**: 违反了商店的内容政策
