# md-cli

A powerful yet simple tool for rendering Markdown documents locally during development.

## Installation

To get started with `md-cli`, you can install it either globally or locally, depending on your needs.

### Install locally

If you only need it for a specific project, you can install it locally by running:

```bash
npm install @doocs/md-cli
```

### Install globally

For global access across all your projects, install it globally with:

```bash
npm install -g @doocs/md-cli
```

## Usage

Once installed, running `md-cli` is a breeze. Here’s how to get started:

### Default setup

To launch `md-cli` with the default settings, simply run:

```bash
md-cli
```

### Custom port

If you prefer to run `md-cli` on a different port, say `8899`, just specify it like this:

```bash
md-cli port=8899
```

## Maintainers

- [yanglbme](https://github.com/yanglbme) – Core maintainer.
- [YangFong](https://github.com/yangfong) – Core maintainer.
- [xw](https://github.com/wll8) – Contributor.
- [thinkasany](https://www.npmjs.com/~thinkerwing) – Contributor.
