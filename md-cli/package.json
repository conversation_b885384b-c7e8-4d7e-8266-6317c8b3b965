{"name": "@doocs/md-cli", "version": "0.1.3", "description": "WeChat Markdown Editor | 一款高度简洁的微信 Markdown 编辑器：支持 Markdown 语法、自定义主题样式、内容管理、多图床、AI 助手等特性", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "bin": {"md-cli": "index.js"}, "files": ["dist", "public", "index.js", "mm.config.js", "util.js"], "keywords": [], "author": "wll8", "license": "ISC", "dependencies": {"@wll8/process-manager": "^1.0.4", "form-data": "4.0.2", "get-port": "5.1.1", "mockm": "^1.1.27-alpha.12", "node-fetch": "^3.3.2"}}