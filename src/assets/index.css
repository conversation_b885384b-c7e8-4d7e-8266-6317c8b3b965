@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;

    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;

    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;

    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border:0 0% 89.8%;
    --input:0 0% 89.8%;
    --ring:0 0% 3.9%;
    --radius: 0.5rem;

    --blockquote-background: #f7f7f7;
  }

  .dark {
    --background:0 0% 3.9%;
    --foreground:0 0% 98%;

    --card:0 0% 3.9%;
    --card-foreground:0 0% 98%;

    --popover:0 0% 3.9%;
    --popover-foreground:0 0% 98%;

    --primary:0 0% 98%;
    --primary-foreground:0 0% 9%;

    --secondary:0 0% 14.9%;
    --secondary-foreground:0 0% 98%;

    --muted:0 0% 14.9%;
    --muted-foreground:0 0% 63.9%;

    --accent:0 0% 14.9%;
    --accent-foreground:0 0% 98%;

    --destructive:0 62.8% 30.6%;
    --destructive-foreground:0 0% 98%;

    --border:0 0% 14.9%;
    --input:0 0% 14.9%;
    --ring:0 0% 83.1%;

    --blockquote-background: #212121;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* CommonMark Admonition 样式 (使用独立的div结构) */
.admonition {
  @apply border-0 rounded bg-white text-gray-800 p-0;
  margin: 1.5625em 0;
  box-shadow: 2px 2px 8px rgba(0,0,0,0.15), 0 1px 3px rgba(0,0,0,0.1);
  page-break-inside: avoid;
  font-style: normal;
  font-size: 14px; /* 比正常文字小一号 */
}

.admonition-note {
  @apply border-l-4 border-blue-500;
}

.admonition-tip {
  @apply border-l-4 border-green-600;
}

.admonition-important {
  @apply border-l-4 border-purple-600;
}

.admonition-warning {
  @apply border-l-4 border-orange-500;
}

.admonition-caution {
  @apply border-l-4 border-red-600;
}

.admonition-question {
  @apply border-l-4 border-green-300;
}

.admonition-hint {
  @apply border-l-4 border-green-600;
}

.admonition-example {
  @apply border-l-4 border-purple-500;
}

.admonition-abstract {
  @apply border-l-4 border-cyan-600;
}

.admonition-title {
  @apply flex items-center gap-2 m-0 px-4 mb-2 py-2 font-medium min-w-0 rounded-t;
  box-shadow: 0 1px 3px rgba(0,0,0,0.08);
  font-size: 15px; /* 标题比内容稍大，但仍比正常文字小 */
}

.admonition-note .admonition-title {
  @apply text-blue-600 bg-blue-50;
}

.admonition-tip .admonition-title {
  @apply text-green-600 bg-green-50;
}

.admonition-important .admonition-title {
  @apply text-purple-600 bg-purple-50;
}

.admonition-warning .admonition-title {
  @apply text-orange-500 bg-orange-50;
}

.admonition-caution .admonition-title {
  @apply text-red-600 bg-red-50;
}

.admonition-question .admonition-title {
  @apply text-green-500 bg-green-50;
}

.admonition-hint .admonition-title {
  @apply text-green-600 bg-green-50;
}

.admonition-example .admonition-title {
  @apply text-purple-500 bg-purple-50;
}

.admonition-abstract .admonition-title {
  @apply text-cyan-600 bg-cyan-50;
}

.admonition-content {
  @apply m-0 px-4 pb-4;
}
