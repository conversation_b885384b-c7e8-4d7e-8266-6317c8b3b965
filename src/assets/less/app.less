* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body {
  height: 100%;
  font-family: 'PingFang SC', BlinkMacSystemFont, Roboto, 'Helvetica Neue',
    sans-serif;
}

input,
button,
textarea {
  font-family: inherit;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: normal;
}

em {
  font-style: normal !important;
}

section {
  height: 100%;
}

.web-title {
  margin: 0 15px 0 5px;
}

.web-icon {
  width: auto;
  height: 1.5rem;
  vertical-align: middle;
}

#editor {
  display: block;
  height: 100%;
  width: 100%;
  padding: 10px;
  border: none;
}

.ctrl {
  flex-basis: 60px;
  flex-grow: 1;
  flex-shrink: 1;
  display: flex;
  align-items: center;
}

.preview-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  overflow-y: scroll;
  width: 100%;
}

.hint {
  opacity: 0.6;
  margin: 20px 0;
}

.preview {
  position: relative;
  // margin: 0 -20px;
  // width: 375px;
  min-height: 100%;
  margin: 0 auto;
  padding: 20px;
  font-size: 14px;
  box-sizing: border-box;
  outline: none;
  transition: all 300ms ease-in-out;
  word-wrap: break-word;
}

.preview table {
  margin-bottom: 10px;
  border-collapse: collapse;
  display: table;
  width: 100% !important;
}
