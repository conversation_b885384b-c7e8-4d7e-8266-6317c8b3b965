import { DEFAULT_SERVICE_ENDPOINT } from '@/constants/AIConfig'

export interface ServiceOption {
  value: string
  label: string
  endpoint: string
  models: string[]
}

export const serviceOptions: ServiceOption[] = [
  {
    value: `default`,
    label: `默认服务（无需配置 sk）`,
    endpoint: DEFAULT_SERVICE_ENDPOINT,
    models: [
      `Qwen/Qwen2.5-7B-Instruct`,
      `Qwen/Qwen2.5-Coder-7B-Instruct`,
      `Qwen/Qwen2-7B-Instruct`,
      `Qwen/Qwen2-1.5B-Instruct`,
      `deepseek-ai/DeepSeek-R1-Distill-Qwen-7B`,
      `deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B`,
      `THUDM/GLM-Z1-9B-0414`,
      `THUDM/GLM-4-9B-0414`,
      `internlm/internlm2_5-7b-chat`,
      `qwen/qwen3-30b-a3b:free`,
      `qwen/qwen3-8b:free`,
      `qwen/qwen3-14b:free`,
      `qwen/qwen3-32b:free`,
      `qwen/qwen3-235b-a22b:free`,
      `tngtech/deepseek-r1t-chimera:free`,
      `thudm/glm-z1-9b:free`,
      `thudm/glm-z1-32b:free`,
      `thudm/glm-4-9b:free`,
      `thudm/glm-4-32b:free`,
      `microsoft/mai-ds-r1:free`,
      `arliai/qwq-32b-arliai-rpr-v1:free`,
      `nvidia/llama-3.3-nemotron-super-49b-v1:free`,
      `nvidia/llama-3.1-nemotron-ultra-253b-v1:free`,
      `meta-llama/llama-4-maverick:free`,
      `meta-llama/llama-4-scout:free`,
      `deepseek/deepseek-v3-base:free`,
      `qwen/qwen2.5-vl-3b-instruct:free`,
      `qwen/qwen2.5-vl-32b-instruct:free`,
    ],
  },
  {
    value: `deepseek`,
    label: `DeepSeek`,
    endpoint: `https://api.deepseek.com/v1`,
    models: [`deepseek-chat`, `deepseek-reasoner`],
  },
  {
    value: `openai`,
    label: `OpenAI`,
    endpoint: `https://api.openai.com/v1`,
    models: [`gpt-4.1`, `gpt-4.1-mini`, `gpt-4.1-nano`, `gpt-4-turbo`, `gpt-4o`, `gpt-3.5-turbo`],
  },
  {
    value: `qwen`,
    label: `通义千问`,
    endpoint: `https://dashscope.aliyuncs.com/compatible-mode/v1`,
    models: [
      `qwen-vl-max-2025-04-02`,
      `deepseek-v3`,
      `deepseek-r1-distill-llama-70b`,
      `deepseek-r1-distill-qwen-32b`,
      `deepseek-r1-distill-qwen-14b`,
      `deepseek-r1-distill-llama-8b`,
      `deepseek-r1-distill-qwen-1.5b`,
      `deepseek-r1-distill-qwen-7b`,
      `deepseek-r1`,
      `qwen1.5-7b-chat`,
      `qwen-coder-plus-1106`,
      `qwen-coder-plus`,
      `qwen-coder-plus-latest`,
      `qwen2.5-coder-3b-instruct`,
      `qwen2.5-coder-0.5b-instruct`,
      `qwen2.5-coder-14b-instruct`,
      `qwen2.5-coder-32b-instruct`,
      `qwen-coder-turbo-0919`,
      `qwen2.5-0.5b-instruct`,
      `qwen2.5-1.5b-instruct`,
      `qwen2.5-3b-instruct`,
      `qwen2.5-7b-instruct`,
      `qwen2.5-14b-instruct`,
      `qwen2.5-32b-instruct`,
      `qwen2.5-72b-instruct`,
      `qwen2.5-coder-7b-instruct`,
      `qwen2.5-math-1.5b-instruct`,
      `qwen2.5-math-7b-instruct`,
      `qwen2.5-math-72b-instruct`,
      `qwen-turbo-0919`,
      `qwen-turbo-latest`,
      `qwen-plus-0919`,
      `qwen-plus-latest`,
      `qwen-max-0919`,
      `qwen-max-latest`,
      `qwen-coder-turbo`,
      `qwen-coder-turbo-latest`,
      `qwen-math-turbo-0919`,
      `qwen-math-turbo`,
      `qwen-math-turbo-latest`,
      `qwen-math-plus-0919`,
      `qwen-math-plus`,
      `qwen-math-plus-latest`,
      `qwen2-57b-a14b-instruct`,
      `qwen2-72b-instruct`,
      `qwen2-7b-instruct`,
      `qwen2-0.5b-instruct`,
      `qwen2-1.5b-instruct`,
      `qwen-long`,
      `qwen-vl-max`,
      `qwen-vl-plus`,
      `qwen-max-0428`,
      `qwen1.5-110b-chat`,
      `qwen-72b-chat`,
      `codeqwen1.5-7b-chat`,
      `qwen1.5-0.5b-chat`,
      `qwen-1.8b-chat`,
      `qwen-1.8b-longcontext-chat`,
      `qwen-7b-chat`,
      `qwen-14b-chat`,
      `qwen1.5-14b-chat`,
      `qwen1.5-1.8b-chat`,
      `qwen1.5-32b-chat`,
      `qwen1.5-72b-chat`,
      `qwen-max-1201`,
      `qwen-max-longcontext`,
      `qwen-max-0403`,
      `qwen-max-0107`,
      `qwen-turbo`,
      `qwen-max`,
      `qwen-plus`,
    ],
  },
  {
    value: `hunyuan`,
    label: `腾讯混元`,
    endpoint: `https://api.hunyuan.cloud.tencent.com/v1`,
    models: [
      `hunyuan-pro`,
      `hunyuan-vision`,
      `hunyuan-lite`,
      `hunyuan-standard`,
      `hunyuan-standard-32K`,
      `hunyuan-standard-256k`,
      `hunyuan-code`,
      `hunyuan-role`,
      `hunyuan-functioncall`,
      `hunyuan-turbo-vision`,
      `hunyuan-turbo`,
    ],
  },
  {
    value: `doubao`,
    label: `火山方舟`,
    endpoint: `https://ark.cn-beijing.volces.com/api/v3`,
    models: [
      `doubao-1-5-thinking-pro-250415`,
      `doubao-1-5-thinking-pro-m-250415`,
      `deepseek-r1-250120`,
      `deepseek-r1-distill-qwen-32b-250120`,
      `deepseek-r1-distill-qwen-7b-250120`,
      `deepseek-v3-250324`,
      `deepseek-v3-241226`,
      `doubao-1-5-vision-pro-250328`,
      `doubao-1-5-vision-lite-250315`,
      `doubao-1-5-vision-pro-32k-250115`,
      `doubao-1-5-ui-tars-250328`,
      `doubao-vision-pro-32k-241028`,
      `doubao-vision-lite-32k-241015`,
      `doubao-1-5-pro-32k-250115`,
      `doubao-1-5-pro-256k-250115`,
      `doubao-1-5-lite-32k-250115`,
      `doubao-pro-4k-240515`,
      `doubao-pro-32k-241215`,
      `doubao-pro-32k-240828`,
      `doubao-pro-32k-240615`,
      `doubao-pro-256k-241115`,
      `doubao-lite-4k-character-240828`,
      `doubao-lite-32k-240828`,
      `doubao-lite-32k-character-241015`,
      `doubao-lite-128k-240828`,
      `moonshot-v1-8k`,
      `moonshot-v1-32k`,
      `moonshot-v1-128k`,
    ],
  },
  {
    value: `siliconflow`,
    label: `硅基流动`,
    endpoint: `https://api.siliconflow.cn/v1`,
    models: [
      `Qwen/Qwen3-235B-A22B`,
      `Qwen/Qwen3-30B-A3B`,
      `Qwen/Qwen3-32B`,
      `Qwen/Qwen3-14B`,
      `Qwen/Qwen3-8B`,
      `THUDM/GLM-Z1-32B-0414`,
      `THUDM/GLM-4-32B-0414`,
      `THUDM/GLM-Z1-Rumination-32B-0414`,
      `THUDM/GLM-4-9B-0414`,
      `Qwen/QwQ-32B`,
      `Pro/deepseek-ai/DeepSeek-R1`,
      `Pro/deepseek-ai/DeepSeek-V3`,
      `deepseek-ai/DeepSeek-R1`,
      `deepseek-ai/DeepSeek-V3`,
      `deepseek-ai/DeepSeek-R1-Distill-Qwen-32B`,
      `deepseek-ai/DeepSeek-R1-Distill-Qwen-14B`,
      `deepseek-ai/DeepSeek-R1-Distill-Qwen-7B`,
      `deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B`,
      `Pro/deepseek-ai/DeepSeek-R1-Distill-Qwen-7B`,
      `Pro/deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B`,
      `deepseek-ai/DeepSeek-V2.5`,
      `Qwen/Qwen2.5-72B-Instruct-128K`,
      `Qwen/Qwen2.5-72B-Instruct`,
      `Qwen/Qwen2.5-32B-Instruct`,
      `Qwen/Qwen2.5-14B-Instruct`,
      `Qwen/Qwen2.5-7B-Instruct`, // ✅ 免费推荐
      `Qwen/Qwen2.5-Coder-32B-Instruct`,
      `Qwen/Qwen2.5-Coder-7B-Instruct`,
      `Qwen/Qwen2-7B-Instruct`,
      `Qwen/Qwen2-1.5B-Instruct`,
      `Qwen/QwQ-32B-Preview`,
      `TeleAI/TeleChat2`,
      `THUDM/glm-4-9b-chat`,
      `Vendor-A/Qwen/Qwen2.5-72B-Instruct`,
      `internlm/internlm2_5-7b-chat`,
      `internlm/internlm2_5-20b-chat`,
      `Pro/Qwen/Qwen2.5-7B-Instruct`,
      `Pro/Qwen/Qwen2-7B-Instruct`,
      `Pro/Qwen/Qwen2-1.5B-Instruct`,
      `Pro/THUDM/chatglm3-6b`,
      `Pro/THUDM/glm-4-9b-chat`,
    ],
  },
  {
    value: `bigmodel`,
    label: `智谱 AI`,
    endpoint: `https://open.bigmodel.cn/api/paas/v4/`,
    models: [
      `glm-4-plus`,
      `glm-4-0520`,
      `glm-4`,
      `glm-4-air`,
      `glm-4-airx`,
      `glm-4-long`,
      `glm-4-flashx`,
      `glm-4-flash`,
    ],
  },
  {
    value: `baichuan`,
    label: `百川智能`,
    endpoint: `https://api.baichuan-ai.com/v1`,
    models: [
      `Baichuan4`,
      `Baichuan3-Turbo`,
      `Baichuan3-Turbo-128k`,
      `Baichuan2-Turbo`,
    ],
  },

  {
    value: `lingyiwanwu`,
    label: `零一万物`,
    endpoint: `https://api.lingyiwanwu.com/v1`,
    models: [
      `yi-lightning`,
    ],
  },

  {
    value: `moonshot`,
    label: `月之暗面`,
    endpoint: `https://api.moonshot.cn/v1`,
    models: [
      `moonshot-v1-8k`,
      `moonshot-v1-32k`,
      `moonshot-v1-128k`,
    ],
  },
  {
    value: `ernie`,
    label: `百度千帆`,
    endpoint: `https://qianfan.baidubce.com/v2`,
    models: [
      `ernie-4.5-turbo-128k`,
      `ernie-4.5-turbo-32k`,
      `ernie-4.5-8k-preview`,
      `ernie-4.0-8k`,
      `ernie-4.0-8k-latest`,
      `ernie-4.0-8k-preview`,
      `ernie-4.0-turbo-128k`,
      `ernie-4.0-turbo-8k`,
      `ernie-4.0-turbo-8k-latest`,
      `ernie-4.0-turbo-8k-preview`,
      `ernie-3.5-128k`,
      `ernie-3.5-8k`,
      `ernie-3.5-8k-preview`,
      `ernie-speed-128k`,
      `ernie-speed-8k`,
      `ernie-speed-pro-128k`,
      `ernie-lite-8k`,
      `ernie-lite-pro-128k`,
      `ernie-tiny-8k`,
      `ernie-novel-8k`,
    ],
  },
  {
    value: `custom`,
    label: `自定义兼容 OpenAI API 的服务`,
    endpoint: ``,
    models: [],
  },
]

export const DEFAULT_SERVICE_MODEL = serviceOptions[0].models[0]
