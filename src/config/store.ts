export const storeLabels: Record<string, string> = {
  // Main store states
  isDark: `深色模式`,
  isEditOnLeft: `左侧编辑`,
  isMacCodeBlock: `Mac 代码块`,
  isCiteStatus: `微信外链接底部引用状态`,
  isCountStatus: `字数统计状态`,
  isUseIndent: `使用缩进`,
  isOpenRightSlider: `开启右侧滑块`,
  isOpenPostSlider: `开启右侧发布滑块`,
  showAIToolbox: `AI 工具箱状态`,
  theme: `主题`,
  fontFamily: `字体`,
  fontSize: `字体大小`,
  primaryColor: `自定义主题色`,
  codeBlockTheme: `代码块主题`,
  legend: `图注格式`,
  fontSizeNumber: `字体大小`,
  currentPostId: `当前文章 ID`,
  currentPostIndex: `当前文章索引`,
  posts: `内容列表`,
  cssContentConfig: `自定义 CSS`,
  titleList: `文章标题列表`,
  readingTime: `阅读时间`,

  // Display store states
  isShowCssEditor: `显示 CSS 编辑器`,
  isShowInsertFormDialog: `显示插入表单对话框`,
  isShowInsertMpCardDialog: `显示插入公众号名片对话框`,
  isShowUploadImgDialog: `显示上传图片对话框`,
  aiDialogVisible: `AI 对话框可见`,
}
